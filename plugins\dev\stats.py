from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import count_users, count_groups, get_all_users, get_all_groups


@Client.on_message(command_handler(["users", "عدد المستخدمين"]) & dev)
@dev_only
async def users_count_command(client: Client, message: Message):
    """معالجة أمر عدد المستخدمين"""
    
    users_count = await count_users()

    await message.reply_text(
        f"👥 **عدد المستخدمين:** `{users_count}`"
    )


@Client.on_message(command_handler(["groups", "عدد المجموعات"]) & dev)
@dev_only
async def groups_count_command(client: Client, message: Message):
    """معالجة أمر عدد المجموعات"""
    
    groups_count = await count_groups()

    await message.reply_text(
        f"👥 **عدد المجموعات:** `{groups_count}`"
    )


@Client.on_message(command_handler(["stats", "الاحصائيات"]) & dev)
@dev_only
async def stats_command(client: Client, message: Message):
    """معالجة أمر الإحصائيات"""
    
    users_count = await count_users()
    groups_count = await count_groups()

    
    bot_info = await client.get_me()

    stats_text = (
        f"📊 **إحصائيات {bot_info.first_name}**\n\n"
        f"👤 **عدد المستخدمين:** `{users_count}`\n"
        f"👥 **عدد المجموعات:** `{groups_count}`\n"
        f"🤖 **معرف البوت:** `{bot_info.id}`\n"
        f"⚡ **اسم المستخدم:** @{bot_info.username}\n"
    )

    await message.reply_text(stats_text)


@Client.on_message(command_handler(["userlist", "قائمة المستخدمين"]) & dev)
@dev_only
async def user_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المستخدمين"""
    
    users = await get_all_users()

    if not users:
        await message.reply_text("⚠️ **لا يوجد مستخدمين.**")
        return

    
    users_text = "👥 **قائمة المستخدمين:**\n\n"

    for i, user_id in enumerate(users[:50], 1):
        users_text += f"{i}. `{user_id}`\n"

    if len(users) > 50:
        users_text += f"\n⚠️ **تم عرض 50 مستخدم فقط من أصل {len(users)}.**"

    await message.reply_text(users_text)


@Client.on_message(command_handler(["grouplist", "قائمة المجموعات"]) & dev)
@dev_only
async def group_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المجموعات"""
    
    groups = await get_all_groups()

    if not groups:
        await message.reply_text("⚠️ **لا يوجد مجموعات.**")
        return

    
    groups_text = "👥 **قائمة المجموعات:**\n\n"

    for i, group_id in enumerate(groups[:50], 1):
        try:
            chat = await client.get_chat(group_id)
            group_name = chat.title
            groups_text += f"{i}. `{group_id}` - {group_name}\n"
        except Exception:
            groups_text += f"{i}. `{group_id}` - غير معروف\n"

    if len(groups) > 50:
        groups_text += f"\n⚠️ **تم عرض 50 مجموعة فقط من أصل {len(groups)}.**"

    await message.reply_text(groups_text)
