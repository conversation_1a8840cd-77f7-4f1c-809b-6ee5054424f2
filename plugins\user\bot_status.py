import time
from datetime import datetime
from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.command_handler import command_handler
from database.settings_db import get_bot_status_settings, get_bot_name
from helpers.middleware import not_banned_or_muted


@Client.on_message(command_handler(["بوت", "bot"]))
async def bot_status_command(client: Client, message: Message):
    """أمر فحص حالة البوت"""

    # التحقق من الحظر والكتم
    from database.users_db import is_user_banned, is_developer
    from database.redis_db import redis_get

    if message.from_user:
        # تجاهل فحص المطورين
        if not await is_developer(message.from_user.id):
            # فحص الحظر
            if await is_user_banned(message.from_user.id):
                return

            # فحص الكتم (في المجموعات فقط)
            if message.chat.type in ["group", "supergroup"]:
                mute_key = f"muted_user:{message.from_user.id}"
                if await redis_get(mute_key):
                    try:
                        await message.delete()
                    except:
                        pass
                    return

    # الحصول على إعدادات أمر البوت
    settings = await get_bot_status_settings()
    bot_name = await get_bot_name()

    # إنشاء الرسالة
    if settings.get("custom_message"):
        # استخدام الرسالة المخصصة
        status_text = settings["custom_message"]
    else:
        # الرسالة الافتراضية
        uptime = time.time() - client.start_time if hasattr(client, 'start_time') else 0
        uptime_str = format_uptime(uptime)

        status_text = f"""
🤖 **{bot_name}**

✅ **الحالة:** يعمل بشكل طبيعي
⏰ **وقت التشغيل:** {uptime_str}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d')}
🕐 **الوقت:** {datetime.now().strftime('%H:%M:%S')}

🔰 **البوت يعمل بكفاءة عالية!**
        """.strip()

    # إرسال الرد
    if settings.get("reply_with_photo", True):
        # الرد بصورة البوت
        try:
            bot_photos = await client.get_chat_photos("me", limit=1)
            if bot_photos:
                await message.reply_photo(
                    photo=bot_photos[0].file_id,
                    caption=status_text
                )
            else:
                # لا توجد صورة للبوت، إرسال رسالة نصية
                await message.reply_text(status_text)
        except:
            # في حالة فشل إرسال الصورة، إرسال رسالة نصية
            await message.reply_text(status_text)
    else:
        # الرد برسالة نصية فقط
        await message.reply_text(status_text)


def format_uptime(seconds):
    """تنسيق وقت التشغيل"""
    if seconds < 60:
        return f"{int(seconds)} ثانية"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        return f"{minutes} دقيقة"
    elif seconds < 86400:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours} ساعة و {minutes} دقيقة"
    else:
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        return f"{days} يوم و {hours} ساعة"


@Client.on_message(command_handler(["ping", "بينغ"]))
async def ping_command(client: Client, message: Message):
    """أمر فحص سرعة الاستجابة"""

    # التحقق من الحظر والكتم
    from database.users_db import is_user_banned, is_developer
    from database.redis_db import redis_get

    if message.from_user:
        # تجاهل فحص المطورين
        if not await is_developer(message.from_user.id):
            # فحص الحظر
            if await is_user_banned(message.from_user.id):
                return

            # فحص الكتم (في المجموعات فقط)
            if message.chat.type in ["group", "supergroup"]:
                mute_key = f"muted_user:{message.from_user.id}"
                if await redis_get(mute_key):
                    try:
                        await message.delete()
                    except:
                        pass
                    return

    start_time = time.time()

    # إرسال رسالة مؤقتة
    temp_msg = await message.reply_text("🏓 **جاري فحص سرعة الاستجابة...**")

    # حساب الوقت المستغرق
    end_time = time.time()
    ping_time = round((end_time - start_time) * 1000, 2)

    # تحديث الرسالة
    await temp_msg.edit_text(
        f"🏓 **Pong!**\n"
        f"⚡ **سرعة الاستجابة:** `{ping_time}ms`"
    )
