import asyncio
from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import get_all_users, get_all_groups
from database.redis_db import redis_sadd, redis_smembers, redis_srem
from utils.logger import log_broadcast


CHANNELS_SET = "channels"


async def add_channel(channel_id, title=None, username=None):
    """إضافة قناة إلى قاعدة البيانات"""
    await redis_sadd(CHANNELS_SET, channel_id)
    return True


async def get_all_channels():
    """الحصول على جميع القنوات"""
    return await redis_smembers(CHANNELS_SET)


async def remove_channel(channel_id):
    """إزالة قناة من قاعدة البيانات"""
    await redis_srem(CHANNELS_SET, channel_id)
    return True


def get_message_type(message):
    """تحديد نوع الرسالة"""
    if message.text:
        return "text"
    elif message.photo:
        return "photo"
    elif message.video:
        return "video"
    elif message.audio:
        return "audio"
    elif message.voice:
        return "voice"
    elif message.document:
        return "document"
    elif message.sticker:
        return "sticker"
    elif message.animation:
        return "animation"
    elif message.video_note:
        return "video_note"
    elif message.location:
        return "location"
    elif message.contact:
        return "contact"
    elif message.poll:
        return "poll"
    else:
        return "unknown"


async def send_message_by_type(client, chat_id, message, message_type):
    """إرسال رسالة حسب نوعها"""
    try:
        if message_type == "text":
            await client.send_message(
                chat_id=chat_id,
                text=message.text,
                entities=message.entities,
                disable_web_page_preview=True
            )
        elif message_type == "photo":
            await client.send_photo(
                chat_id=chat_id,
                photo=message.photo.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities
            )
        elif message_type == "video":
            await client.send_video(
                chat_id=chat_id,
                video=message.video.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities,
                duration=message.video.duration,
                width=message.video.width,
                height=message.video.height
            )
        elif message_type == "audio":
            await client.send_audio(
                chat_id=chat_id,
                audio=message.audio.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities,
                duration=message.audio.duration,
                performer=message.audio.performer,
                title=message.audio.title
            )
        elif message_type == "voice":
            await client.send_voice(
                chat_id=chat_id,
                voice=message.voice.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities,
                duration=message.voice.duration
            )
        elif message_type == "document":
            await client.send_document(
                chat_id=chat_id,
                document=message.document.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities,
                file_name=message.document.file_name
            )
        elif message_type == "sticker":
            await client.send_sticker(
                chat_id=chat_id,
                sticker=message.sticker.file_id
            )
        elif message_type == "animation":
            await client.send_animation(
                chat_id=chat_id,
                animation=message.animation.file_id,
                caption=message.caption,
                caption_entities=message.caption_entities,
                duration=message.animation.duration,
                width=message.animation.width,
                height=message.animation.height
            )
        elif message_type == "video_note":
            await client.send_video_note(
                chat_id=chat_id,
                video_note=message.video_note.file_id,
                duration=message.video_note.duration,
                length=message.video_note.length
            )
        elif message_type == "location":
            await client.send_location(
                chat_id=chat_id,
                latitude=message.location.latitude,
                longitude=message.location.longitude
            )
        elif message_type == "contact":
            await client.send_contact(
                chat_id=chat_id,
                phone_number=message.contact.phone_number,
                first_name=message.contact.first_name,
                last_name=message.contact.last_name
            )
        elif message_type == "poll":
            await client.send_poll(
                chat_id=chat_id,
                question=message.poll.question,
                options=[option.text for option in message.poll.options],
                is_anonymous=message.poll.is_anonymous,
                type=message.poll.type,
                allows_multiple_answers=message.poll.allows_multiple_answers
            )
        else:
            
            await message.copy(chat_id)

        return True
    except Exception as e:
        print(f"خطأ في إرسال الرسالة إلى {chat_id}: {e}")
        return False


@Client.on_message(command_handler(["broadcast", "اذاعة"]) & dev)
@dev_only
async def broadcast_command(client: Client, message: Message):
    """معالجة أمر الإذاعة العامة المحسنة مع دعم الوسائط"""
    
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "**طرق الاستخدام:**\n"
            "• `اذاعة مرحبًا بالجميع!` - إذاعة نص\n"
            "• الرد على رسالة نصية مع `اذاعة`\n"
            "• الرد على صورة/فيديو/ملف مع `اذاعة`\n"
            "• الرد على رسالة مُعاد توجيهها مع `اذاعة`\n\n"
            "**يدعم جميع أنواع الوسائط:** النصوص، الصور، الفيديوهات، الملفات، الملصقات، والمزيد!"
        )
        return

    
    broadcast_message = None
    broadcast_text = None
    message_type = "text"
    is_forwarded = False

    if message.reply_to_message:
        broadcast_message = message.reply_to_message
        message_type = get_message_type(broadcast_message)
        is_forwarded = bool(broadcast_message.forward_from or broadcast_message.forward_from_chat)
    else:
        broadcast_text = " ".join(message.command[1:])
        
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    
    users = await get_all_users()
    groups = await get_all_groups()
    channels = await get_all_channels()
    all_chats = list(users) + list(groups) + list(channels)

    
    content_type_display = {
        "text": "نص",
        "photo": "صورة",
        "video": "فيديو",
        "audio": "ملف صوتي",
        "voice": "رسالة صوتية",
        "document": "ملف",
        "sticker": "ملصق",
        "animation": "صورة متحركة",
        "video_note": "رسالة فيديو",
        "location": "موقع",
        "contact": "جهة اتصال",
        "poll": "استطلاع رأي"
    }.get(message_type, "محتوى")

    
    forward_text = " (مُعاد توجيه)" if is_forwarded else ""
    progress_message = await message.reply_text(
        f"📡 **جاري إرسال الإذاعة...**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n"
        f"👥 **المستخدمين:** {len(users)}\n"
        f"🏘 **المجموعات:** {len(groups)}\n"
        f"📢 **القنوات:** {len(channels)}\n"
        f"📊 **إجمالي:** {len(all_chats)} دردشة\n\n"
        f"⏳ **جاري البدء...**"
    )

    
    success = 0
    failed = 0
    users_success = 0
    groups_success = 0
    channels_success = 0

    
    for i, chat_id in enumerate(all_chats):
        try:
            
            chat_type = "user"
            if str(chat_id) in groups:
                chat_type = "group"
            elif str(chat_id) in channels:
                chat_type = "channel"

            
            if broadcast_text:
                
                await client.send_message(
                    chat_id=chat_id,
                    text=broadcast_text
                )
            else:
                
                if is_forwarded:
                    
                    await broadcast_message.forward(chat_id)
                else:
                    
                    await send_message_by_type(client, chat_id, broadcast_message, message_type)

            success += 1

            
            if chat_type == "user":
                users_success += 1
            elif chat_type == "group":
                groups_success += 1
            elif chat_type == "channel":
                channels_success += 1

        except Exception as e:
            failed += 1
            print(f"فشل إرسال الإذاعة إلى {chat_id}: {e}")

        
        if (success + failed) % 20 == 0 or (success + failed) == len(all_chats):
            try:
                await progress_message.edit_text(
                    f"📡 **جاري إرسال الإذاعة...**\n\n"
                    f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
                    f"✅ **تم بنجاح:** {success}\n"
                    f"👤 **مستخدمين:** {users_success}\n"
                    f"🏘 **مجموعات:** {groups_success}\n"
                    f"📢 **قنوات:** {channels_success}\n\n"
                    f"❌ **فشل:** {failed}\n"
                    f"⏳ **المتبقي:** {len(all_chats) - (success + failed)}\n\n"
                    f"📊 **التقدم:** {((success + failed) / len(all_chats) * 100):.1f}%"
                )
            except:
                pass

        
        await asyncio.sleep(0.05)

    
    await log_broadcast(
        client,
        f"عامة ({content_type_display})",
        message.from_user.id,
        success,
        failed
    )

    
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة بنجاح!**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
        f"✅ **تم بنجاح:** {success}\n"
        f"👤 **مستخدمين:** {users_success}/{len(users)}\n"
        f"🏘 **مجموعات:** {groups_success}/{len(groups)}\n"
        f"📢 **قنوات:** {channels_success}/{len(channels)}\n\n"
        f"❌ **فشل:** {failed}\n"
        f"📊 **إجمالي:** {len(all_chats)}\n"
        f"🎯 **معدل النجاح:** {(success / len(all_chats) * 100):.1f}%"
    )


@Client.on_message(command_handler(["addchannel", "اضافة قناة"]) & dev)
@dev_only
async def add_channel_command(client: Client, message: Message):
    """معالجة أمر إضافة قناة للإذاعة"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى تحديد معرف القناة أو اسم المستخدم.**\n\n"
            "**أمثلة:**\n"
            "`اضافة قناة @channel_username`\n"
            "`اضافة قناة -1001234567890`\n"
            "أو الرد على رسالة من القناة مع `اضافة قناة`"
        )
        return

    channel_id = None
    channel_info = None

    
    if message.reply_to_message and message.reply_to_message.forward_from_chat:
        if message.reply_to_message.forward_from_chat.type == "channel":
            channel_id = message.reply_to_message.forward_from_chat.id
            channel_info = {
                'title': message.reply_to_message.forward_from_chat.title,
                'username': message.reply_to_message.forward_from_chat.username
            }
    else:
        
        channel_input = message.command[1]

        try:
            if channel_input.startswith("@"):
                
                channel = await client.get_chat(channel_input)
                if channel.type == "channel":
                    channel_id = channel.id
                    channel_info = {
                        'title': channel.title,
                        'username': channel.username
                    }
                else:
                    await message.reply_text("❌ **هذا ليس قناة صحيحة.**")
                    return
            elif channel_input.lstrip('-').isdigit():
                
                channel_id = int(channel_input)
                channel = await client.get_chat(channel_id)
                if channel.type == "channel":
                    channel_info = {
                        'title': channel.title,
                        'username': channel.username
                    }
                else:
                    await message.reply_text("❌ **هذا ليس قناة صحيحة.**")
                    return
            else:
                await message.reply_text("❌ **معرف القناة غير صحيح.**")
                return
        except Exception as e:
            await message.reply_text(f"❌ **خطأ في الوصول للقناة:** `{str(e)}`")
            return

    if not channel_id:
        await message.reply_text("❌ **لم يتم العثور على القناة.**")
        return

    
    channels = await get_all_channels()
    if str(channel_id) in channels:
        await message.reply_text(
            f"⚠️ **القناة {channel_info['title']} موجودة بالفعل في قائمة الإذاعة.**"
        )
        return

    
    await add_channel(channel_id, channel_info['title'], channel_info['username'])

    
    channel_display = f"{channel_info['title']}"
    if channel_info.get('username'):
        channel_display += f" (@{channel_info['username']})"

    await message.reply_text(
        f"✅ **تم إضافة القناة بنجاح!**\n\n"
        f"📢 **اسم القناة:** {channel_display}\n"
        f"🆔 **المعرف:** `{channel_id}`\n\n"
        f"🎯 **ستتم إضافة هذه القناة في الإذاعات المستقبلية.**"
    )


@Client.on_message(command_handler(["removechannel", "حذف قناة"]) & dev)
@dev_only
async def remove_channel_command(client: Client, message: Message):
    """معالجة أمر إزالة قناة من الإذاعة"""
    if len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى تحديد معرف القناة أو اسم المستخدم.**\n\n"
            "**أمثلة:**\n"
            "`حذف قناة @channel_username`\n"
            "`حذف قناة -1001234567890`"
        )
        return

    channel_input = message.command[1]
    channel_id = None

    try:
        if channel_input.startswith("@"):
            
            channel = await client.get_chat(channel_input)
            channel_id = channel.id
        elif channel_input.lstrip('-').isdigit():
            
            channel_id = int(channel_input)
        else:
            await message.reply_text("❌ **معرف القناة غير صحيح.**")
            return
    except Exception as e:
        await message.reply_text(f"❌ **خطأ في الوصول للقناة:** `{str(e)}`")
        return

    
    channels = await get_all_channels()
    if str(channel_id) not in channels:
        await message.reply_text("⚠️ **القناة غير موجودة في قائمة الإذاعة.**")
        return

    
    await remove_channel(channel_id)

    await message.reply_text(
        f"✅ **تم حذف القناة من قائمة الإذاعة بنجاح!**\n\n"
        f"🆔 **المعرف:** `{channel_id}`"
    )


@Client.on_message(command_handler(["channellist", "قائمة القنوات"]) & dev)
@dev_only
async def channel_list_command(client: Client, message: Message):
    """معالجة أمر قائمة القنوات"""
    channels = await get_all_channels()

    if not channels:
        await message.reply_text(
            "⚠️ **لا توجد قنوات مضافة للإذاعة.**\n\n"
            "يمكنك إضافة قناة باستخدام الأمر:\n"
            "`اضافة قناة @channel_username`"
        )
        return

    
    channels_text = f"📢 **قائمة القنوات المضافة للإذاعة ({len(channels)}):**\n\n"

    for i, channel_id in enumerate(channels, 1):
        try:
            
            channel = await client.get_chat(int(channel_id))
            channel_display = f"{channel.title}"
            if channel.username:
                channel_display += f" (@{channel.username})"
            channels_text += f"{i}. {channel_display} - `{channel_id}`\n"
        except:
            
            channels_text += f"{i}. قناة غير معروفة - `{channel_id}`\n"

    await message.reply_text(channels_text)


@Client.on_message(command_handler(["broadcastusers", "اذاعة للمستخدمين"]) & dev)
@dev_only
async def broadcast_users_command(client: Client, message: Message):
    """معالجة أمر الإذاعة للمستخدمين مع دعم الوسائط"""
    
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "**طرق الاستخدام:**\n"
            "• `اذاعة للمستخدمين مرحبًا بالجميع!`\n"
            "• الرد على رسالة نصية مع `اذاعة للمستخدمين`\n"
            "• الرد على صورة/فيديو/ملف مع `اذاعة للمستخدمين`\n"
            "• الرد على رسالة مُعاد توجيهها مع `اذاعة للمستخدمين`"
        )
        return

    
    broadcast_message = None
    broadcast_text = None
    message_type = "text"
    is_forwarded = False

    if message.reply_to_message:
        broadcast_message = message.reply_to_message
        message_type = get_message_type(broadcast_message)
        is_forwarded = bool(broadcast_message.forward_from or broadcast_message.forward_from_chat)
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    
    users = await get_all_users()

    
    content_type_display = {
        "text": "نص",
        "photo": "صورة",
        "video": "فيديو",
        "audio": "ملف صوتي",
        "voice": "رسالة صوتية",
        "document": "ملف",
        "sticker": "ملصق",
        "animation": "صورة متحركة",
        "video_note": "رسالة فيديو",
        "location": "موقع",
        "contact": "جهة اتصال",
        "poll": "استطلاع رأي"
    }.get(message_type, "محتوى")

    
    forward_text = " (مُعاد توجيه)" if is_forwarded else ""
    progress_message = await message.reply_text(
        f"👤 **جاري إرسال الإذاعة للمستخدمين...**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n"
        f"👥 **عدد المستخدمين:** {len(users)}\n\n"
        f"⏳ **جاري البدء...**"
    )

    
    success = 0
    failed = 0

    
    for user_id in users:
        try:
            
            if broadcast_text:
                await client.send_message(
                    chat_id=user_id,
                    text=broadcast_text
                )
            else:
                if is_forwarded:
                    await broadcast_message.forward(user_id)
                else:
                    await send_message_by_type(client, user_id, broadcast_message, message_type)

            success += 1
        except Exception as e:
            failed += 1

        
        if (success + failed) % 20 == 0 or (success + failed) == len(users):
            try:
                await progress_message.edit_text(
                    f"👤 **جاري إرسال الإذاعة للمستخدمين...**\n\n"
                    f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
                    f"✅ **تم بنجاح:** {success}\n"
                    f"❌ **فشل:** {failed}\n"
                    f"⏳ **المتبقي:** {len(users) - (success + failed)}\n\n"
                    f"📊 **التقدم:** {((success + failed) / len(users) * 100):.1f}%"
                )
            except:
                pass

        
        await asyncio.sleep(0.05)

    
    await log_broadcast(
        client,
        f"للمستخدمين ({content_type_display})",
        message.from_user.id,
        success,
        failed
    )

    
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة للمستخدمين بنجاح!**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
        f"✅ **تم بنجاح:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"👥 **إجمالي:** {len(users)}\n"
        f"🎯 **معدل النجاح:** {(success / len(users) * 100):.1f}%"
    )


@Client.on_message(command_handler(["broadcastgroups", "اذاعة للمجموعات"]) & dev)
@dev_only
async def broadcast_groups_command(client: Client, message: Message):
    """معالجة أمر الإذاعة للمجموعات مع دعم الوسائط"""
    
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "**طرق الاستخدام:**\n"
            "• `اذاعة للمجموعات مرحبًا بالجميع!`\n"
            "• الرد على رسالة نصية مع `اذاعة للمجموعات`\n"
            "• الرد على صورة/فيديو/ملف مع `اذاعة للمجموعات`\n"
            "• الرد على رسالة مُعاد توجيهها مع `اذاعة للمجموعات`"
        )
        return

    
    broadcast_message = None
    broadcast_text = None
    message_type = "text"
    is_forwarded = False

    if message.reply_to_message:
        broadcast_message = message.reply_to_message
        message_type = get_message_type(broadcast_message)
        is_forwarded = bool(broadcast_message.forward_from or broadcast_message.forward_from_chat)
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    
    groups = await get_all_groups()

    
    content_type_display = {
        "text": "نص",
        "photo": "صورة",
        "video": "فيديو",
        "audio": "ملف صوتي",
        "voice": "رسالة صوتية",
        "document": "ملف",
        "sticker": "ملصق",
        "animation": "صورة متحركة",
        "video_note": "رسالة فيديو",
        "location": "موقع",
        "contact": "جهة اتصال",
        "poll": "استطلاع رأي"
    }.get(message_type, "محتوى")

    
    forward_text = " (مُعاد توجيه)" if is_forwarded else ""
    progress_message = await message.reply_text(
        f"🏘 **جاري إرسال الإذاعة للمجموعات...**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n"
        f"🏘 **عدد المجموعات:** {len(groups)}\n\n"
        f"⏳ **جاري البدء...**"
    )

    
    success = 0
    failed = 0

    
    for group_id in groups:
        try:
            
            if broadcast_text:
                await client.send_message(
                    chat_id=group_id,
                    text=broadcast_text
                )
            else:
                if is_forwarded:
                    await broadcast_message.forward(group_id)
                else:
                    await send_message_by_type(client, group_id, broadcast_message, message_type)

            success += 1
        except Exception as e:
            failed += 1

        
        if (success + failed) % 15 == 0 or (success + failed) == len(groups):
            try:
                await progress_message.edit_text(
                    f"🏘 **جاري إرسال الإذاعة للمجموعات...**\n\n"
                    f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
                    f"✅ **تم بنجاح:** {success}\n"
                    f"❌ **فشل:** {failed}\n"
                    f"⏳ **المتبقي:** {len(groups) - (success + failed)}\n\n"
                    f"📊 **التقدم:** {((success + failed) / len(groups) * 100):.1f}%"
                )
            except:
                pass

        
        await asyncio.sleep(0.1)

    
    await log_broadcast(
        client,
        f"للمجموعات ({content_type_display})",
        message.from_user.id,
        success,
        failed
    )

    
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة للمجموعات بنجاح!**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
        f"✅ **تم بنجاح:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"🏘 **إجمالي:** {len(groups)}\n"
        f"🎯 **معدل النجاح:** {(success / len(groups) * 100):.1f}%"
    )


@Client.on_message(command_handler(["broadcastchannels", "اذاعة للقنوات"]) & dev)
@dev_only
async def broadcast_channels_command(client: Client, message: Message):
    """معالجة أمر الإذاعة للقنوات مع دعم الوسائط"""
    
    if not message.reply_to_message and len(message.command) < 2:
        await message.reply_text(
            "⚠️ **يرجى الرد على رسالة أو كتابة نص للإذاعة.**\n\n"
            "**طرق الاستخدام:**\n"
            "• `اذاعة للقنوات مرحبًا بالجميع!`\n"
            "• الرد على رسالة نصية مع `اذاعة للقنوات`\n"
            "• الرد على صورة/فيديو/ملف مع `اذاعة للقنوات`\n"
            "• الرد على رسالة مُعاد توجيهها مع `اذاعة للقنوات`\n\n"
            "⚠️ **ملاحظة:** تأكد من إضافة القنوات أولاً باستخدام `اضافة قناة`"
        )
        return

    
    broadcast_message = None
    broadcast_text = None
    message_type = "text"
    is_forwarded = False

    if message.reply_to_message:
        broadcast_message = message.reply_to_message
        message_type = get_message_type(broadcast_message)
        is_forwarded = bool(broadcast_message.forward_from or broadcast_message.forward_from_chat)
    else:
        broadcast_text = " ".join(message.command[1:])
        broadcast_message = await client.send_message(
            chat_id=message.chat.id,
            text=broadcast_text
        )

    
    channels = await get_all_channels()

    if not channels:
        await message.reply_text(
            "⚠️ **لا توجد قنوات مضافة للإذاعة.**\n\n"
            "يمكنك إضافة قناة باستخدام الأمر:\n"
            "`اضافة قناة @channel_username`"
        )
        return

    
    content_type_display = {
        "text": "نص",
        "photo": "صورة",
        "video": "فيديو",
        "audio": "ملف صوتي",
        "voice": "رسالة صوتية",
        "document": "ملف",
        "sticker": "ملصق",
        "animation": "صورة متحركة",
        "video_note": "رسالة فيديو",
        "location": "موقع",
        "contact": "جهة اتصال",
        "poll": "استطلاع رأي"
    }.get(message_type, "محتوى")

    
    forward_text = " (مُعاد توجيه)" if is_forwarded else ""
    progress_message = await message.reply_text(
        f"📢 **جاري إرسال الإذاعة للقنوات...**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n"
        f"📢 **عدد القنوات:** {len(channels)}\n\n"
        f"⏳ **جاري البدء...**"
    )

    
    success = 0
    failed = 0

    
    for channel_id in channels:
        try:
            
            if broadcast_text:
                await client.send_message(
                    chat_id=int(channel_id),
                    text=broadcast_text
                )
            else:
                if is_forwarded:
                    await broadcast_message.forward(int(channel_id))
                else:
                    await send_message_by_type(client, int(channel_id), broadcast_message, message_type)

            success += 1
        except Exception as e:
            failed += 1

        
        if (success + failed) % 5 == 0 or (success + failed) == len(channels):
            try:
                await progress_message.edit_text(
                    f"📢 **جاري إرسال الإذاعة للقنوات...**\n\n"
                    f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
                    f"✅ **تم بنجاح:** {success}\n"
                    f"❌ **فشل:** {failed}\n"
                    f"⏳ **المتبقي:** {len(channels) - (success + failed)}\n\n"
                    f"📊 **التقدم:** {((success + failed) / len(channels) * 100):.1f}%"
                )
            except:
                pass

        
        await asyncio.sleep(0.2)

    
    await log_broadcast(
        client,
        f"للقنوات ({content_type_display})",
        message.from_user.id,
        success,
        failed
    )

    
    await progress_message.edit_text(
        f"✅ **تم إرسال الإذاعة للقنوات بنجاح!**\n\n"
        f"📝 **نوع المحتوى:** {content_type_display}{forward_text}\n\n"
        f"✅ **تم بنجاح:** {success}\n"
        f"❌ **فشل:** {failed}\n"
        f"📢 **إجمالي:** {len(channels)}\n"
        f"🎯 **معدل النجاح:** {(success / len(channels) * 100):.1f}%"
    )
