from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from helpers.decorators import check_banned, check_force_sub, group_only
from helpers.filters import admin
from helpers.command_handler import command_handler
from utils.extraction import extract_song_name
from database.settings_db import is_youtube_enabled, is_audio_enabled
from utils.logger import log_stream


@Client.on_message(command_handler(["play", "تشغيل"]))
@check_banned
@check_force_sub
@group_only
async def play_command(client: Client, message: Message):
    """معالجة أمر التشغيل"""
    
    if not await admin.func(client, message):
        await message.reply_text(
            "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
        )
        return

    
    song_name = await extract_song_name(message)

    if not song_name:
        await message.reply_text(
            "⚠️ **يرجى تحديد اسم الأغنية أو الرابط.**\n\n"
            "مثال: `تشغيل اسم الأغنية`"
        )
        return

    
    if not await is_youtube_enabled() and "youtube.com" in song_name:
        await message.reply_text(
            "⚠️ **تشغيل اليوتيوب معطل حاليًا.**"
        )
        return

    
    if not await is_audio_enabled():
        await message.reply_text(
            "⚠️ **التشغيل الصوتي معطل حاليًا.**"
        )
        return

    
    search_message = await message.reply_text(
        f"🔍 **جاري البحث عن:** `{song_name}`"
    )

    
    

    
    await log_stream(
        client,
        message.chat.id,
        message.chat.title,
        message.from_user.id,
        message.from_user.first_name,
        song_name
    )

    
    buttons = [
        [
            InlineKeyboardButton("⏸ إيقاف مؤقت", callback_data="pause"),
            InlineKeyboardButton("⏭ تخطي", callback_data="skip"),
            InlineKeyboardButton("⏹ إيقاف", callback_data="stop")
        ],
        [
            InlineKeyboardButton("🔄 تكرار", callback_data="repeat"),
            InlineKeyboardButton("🎵 قائمة التشغيل", callback_data="playlist")
        ]
    ]

    
    await search_message.edit_text(
        f"🎵 **تم تشغيل:** `{song_name}`\n"
        f"💭 **بواسطة:** {message.from_user.mention}",
        reply_markup=InlineKeyboardMarkup(buttons)
    )


@Client.on_message(command_handler(["skip", "تخطي"]))
@check_banned
@check_force_sub
@group_only
async def skip_command(client: Client, message: Message):
    """معالجة أمر التخطي"""
    
    if not await admin.func(client, message):
        await message.reply_text(
            "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
        )
        return

    
    

    await message.reply_text(
        "⏭ **تم تخطي الأغنية الحالية.**"
    )


@Client.on_message(command_handler(["pause", "ايقاف مؤقت"]))
@check_banned
@check_force_sub
@group_only
async def pause_command(client: Client, message: Message):
    """معالجة أمر الإيقاف المؤقت"""
    
    if not await admin.func(client, message):
        await message.reply_text(
            "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
        )
        return

    
    

    await message.reply_text(
        "⏸ **تم إيقاف التشغيل مؤقتًا.**"
    )


@Client.on_message(command_handler(["resume", "استئناف"]))
@check_banned
@check_force_sub
@group_only
async def resume_command(client: Client, message: Message):
    """معالجة أمر الاستئناف"""
    
    if not await admin.func(client, message):
        await message.reply_text(
            "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
        )
        return

    
    

    await message.reply_text(
        "▶️ **تم استئناف التشغيل.**"
    )


@Client.on_message(command_handler(["stop", "ايقاف"]))
@check_banned
@check_force_sub
@group_only
async def stop_command(client: Client, message: Message):
    """معالجة أمر الإيقاف"""
    
    if not await admin.func(client, message):
        await message.reply_text(
            "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
        )
        return

    
    

    await message.reply_text(
        "⏹ **تم إيقاف التشغيل.**"
    )


@Client.on_message(command_handler(["queue", "قائمة التشغيل"]))
@check_banned
@check_force_sub
@group_only
async def queue_command(client: Client, message: Message):
    """معالجة أمر قائمة التشغيل"""
    
    

    await message.reply_text(
        "🎵 **قائمة التشغيل:**\n\n"
        "1. أغنية 1\n"
        "2. أغنية 2\n"
        "3. أغنية 3\n\n"
        "⚠️ **هذه قائمة وهمية للعرض فقط.**"
    )


@Client.on_callback_query(filters.regex("^(pause|resume|skip|stop|repeat|playlist)$"))
async def player_callback(client, callback_query):
    """معالجة ردود أزرار التشغيل"""
    
    message = callback_query.message
    chat_id = message.chat.id
    user_id = callback_query.from_user.id

    
    member = await client.get_chat_member(chat_id, user_id)
    if member.status not in ["creator", "administrator"]:
        await callback_query.answer(
            "⚠️ هذا الأمر متاح فقط للمشرفين.",
            show_alert=True
        )
        return

    
    if callback_query.data == "pause":
        
        await callback_query.answer("⏸ تم إيقاف التشغيل مؤقتًا.")

    elif callback_query.data == "resume":
        
        await callback_query.answer("▶️ تم استئناف التشغيل.")

    elif callback_query.data == "skip":
        
        await callback_query.answer("⏭ تم تخطي الأغنية الحالية.")

    elif callback_query.data == "stop":
        
        await callback_query.answer("⏹ تم إيقاف التشغيل.")

    elif callback_query.data == "repeat":
        
        await callback_query.answer("🔄 تم تفعيل/تعطيل التكرار.")

    elif callback_query.data == "playlist":
        
        await callback_query.answer("🎵 جاري عرض قائمة التشغيل.")

        
        buttons = [
            [
                InlineKeyboardButton("🔙 رجوع", callback_data="back_to_player")
            ]
        ]

        await callback_query.edit_message_text(
            "🎵 **قائمة التشغيل:**\n\n"
            "1. أغنية 1\n"
            "2. أغنية 2\n"
            "3. أغنية 3\n\n"
            "⚠️ **هذه قائمة وهمية للعرض فقط.**",
            reply_markup=InlineKeyboardMarkup(buttons)
        )
