"""
وحدة التحكم في تشغيل الموسيقى
تدير جميع أوامر التشغيل والتحكم في الموسيقى
"""

from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from helpers.decorators import check_banned, check_force_sub, group_only
from helpers.filters import admin
from helpers.command_handler import command_handler
from utils.extraction import extract_song_name
from database.settings_db import is_youtube_enabled, is_audio_enabled
from utils.logger import log_stream
from plugins.music.core import music_core
from plugins.music.youtube import search_youtube, download_video_info, is_youtube_url, get_video_info
from database.music_db import (
    add_to_queue, get_current_song, set_current_song,
    get_volume, set_volume, increment_play_count
)


@Client.on_message(command_handler(["play", "تشغيل"]))
@check_banned
@check_force_sub
@group_only
async def play_command(client: Client, message: Message):
    """معالجة أمر التشغيل"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id
        song_name = await extract_song_name(message)

        if not song_name:
            await message.reply_text(
                "⚠️ **يرجى تحديد اسم الأغنية أو الرابط.**\n\n"
                "مثال: `تشغيل اسم الأغنية`"
            )
            return

        # التحقق من الإعدادات
        if not await is_youtube_enabled() and "youtube.com" in song_name:
            await message.reply_text(
                "⚠️ **تشغيل اليوتيوب معطل حاليًا.**"
            )
            return

        if not await is_audio_enabled():
            await message.reply_text(
                "⚠️ **التشغيل الصوتي معطل حاليًا.**"
            )
            return

        # رسالة البحث
        search_message = await message.reply_text(
            f"🔍 **جاري البحث عن:** `{song_name}`"
        )

        try:
            # البحث أو الحصول على معلومات الفيديو
            if is_youtube_url(song_name):
                # رابط مباشر
                video_info = await get_video_info(song_name)
                if not video_info:
                    await search_message.edit_text(
                        "❌ **فشل في الحصول على معلومات الفيديو.**"
                    )
                    return
            else:
                # البحث في يوتيوب
                search_results = await search_youtube(song_name, limit=1)
                if not search_results:
                    await search_message.edit_text(
                        "❌ **لم يتم العثور على نتائج.**"
                    )
                    return
                video_info = search_results[0]

            # تحديث رسالة البحث
            await search_message.edit_text(
                f"⬇️ **جاري تحميل:** `{video_info['title']}`"
            )

            # تحميل الصوت
            download_info = await download_video_info(video_info['url'])
            if not download_info or not download_info.get('audio_path'):
                await search_message.edit_text(
                    "❌ **فشل في تحميل الصوت.**"
                )
                return

            # التحقق من وجود أغنية قيد التشغيل
            current_song = await get_current_song(chat_id)

            if current_song:
                # إضافة إلى قائمة الانتظار
                await add_to_queue(chat_id, download_info)

                await search_message.edit_text(
                    f"✅ **تم إضافة إلى قائمة الانتظار:**\n"
                    f"🎵 `{download_info['title']}`\n"
                    f"👤 {download_info['channel']}\n"
                    f"⏱ {download_info['duration']}\n"
                    f"💭 **بواسطة:** {message.from_user.mention}"
                )
            else:
                # تشغيل مباشر
                success = await music_core.play_audio(chat_id, download_info['audio_path'], download_info)

                if success:
                    # زيادة عداد التشغيل
                    await increment_play_count(chat_id)

                    # أزرار التحكم
                    buttons = [
                        [
                            InlineKeyboardButton("⏸ إيقاف مؤقت", callback_data="pause"),
                            InlineKeyboardButton("⏭ تخطي", callback_data="skip"),
                            InlineKeyboardButton("⏹ إيقاف", callback_data="stop")
                        ],
                        [
                            InlineKeyboardButton("🔊 الصوت", callback_data="volume"),
                            InlineKeyboardButton("🎵 القائمة", callback_data="queue")
                        ]
                    ]

                    await search_message.edit_text(
                        f"🎵 **الآن يتم تشغيل:**\n"
                        f"🎵 `{download_info['title']}`\n"
                        f"👤 {download_info['channel']}\n"
                        f"⏱ {download_info['duration']}\n"
                        f"💭 **بواسطة:** {message.from_user.mention}",
                        reply_markup=InlineKeyboardMarkup(buttons)
                    )
                else:
                    await search_message.edit_text(
                        "❌ **فشل في تشغيل الأغنية.**"
                    )

            # تسجيل العملية
            await log_stream(
                client,
                chat_id,
                message.chat.title,
                message.from_user.id,
                message.from_user.first_name,
                song_name
            )

        except Exception as e:
            await search_message.edit_text(
                f"❌ **خطأ في التشغيل:**\n`{str(e)}`"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في معالجة الأمر:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["skip", "تخطي"]))
@check_banned
@check_force_sub
@group_only
async def skip_command(client: Client, message: Message):
    """معالجة أمر التخطي"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # تخطي الأغنية الحالية
        success = await music_core.skip_audio(chat_id)

        if success:
            await message.reply_text(
                "⏭ **تم تخطي الأغنية الحالية.**"
            )
        else:
            await message.reply_text(
                "❌ **فشل في تخطي الأغنية.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في التخطي:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["pause", "ايقاف مؤقت"]))
@check_banned
@check_force_sub
@group_only
async def pause_command(client: Client, message: Message):
    """معالجة أمر الإيقاف المؤقت"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # إيقاف التشغيل مؤقتاً
        success = await music_core.pause_audio(chat_id)

        if success:
            await message.reply_text(
                "⏸ **تم إيقاف التشغيل مؤقتًا.**"
            )
        else:
            await message.reply_text(
                "❌ **فشل في إيقاف التشغيل.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في الإيقاف المؤقت:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["resume", "استئناف"]))
@check_banned
@check_force_sub
@group_only
async def resume_command(client: Client, message: Message):
    """معالجة أمر الاستئناف"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # استئناف التشغيل
        success = await music_core.resume_audio(chat_id)

        if success:
            await message.reply_text(
                "▶️ **تم استئناف التشغيل.**"
            )
        else:
            await message.reply_text(
                "❌ **فشل في استئناف التشغيل.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في الاستئناف:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["stop", "ايقاف"]))
@check_banned
@check_force_sub
@group_only
async def stop_command(client: Client, message: Message):
    """معالجة أمر الإيقاف"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # إيقاف التشغيل
        success = await music_core.stop_audio(chat_id)

        if success:
            await message.reply_text(
                "⏹ **تم إيقاف التشغيل.**"
            )
        else:
            await message.reply_text(
                "❌ **فشل في إيقاف التشغيل.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في الإيقاف:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["volume", "الصوت", "مستوى الصوت"]))
@check_banned
@check_force_sub
@group_only
async def volume_command(client: Client, message: Message):
    """معالجة أمر مستوى الصوت"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # استخراج مستوى الصوت من الرسالة
        args = message.text.split()[1:] if len(message.text.split()) > 1 else []

        if not args:
            # عرض مستوى الصوت الحالي
            current_volume = await get_volume(chat_id)
            await message.reply_text(
                f"🔊 **مستوى الصوت الحالي:** {current_volume}%"
            )
            return

        try:
            volume = int(args[0])
            if volume < 0 or volume > 200:
                await message.reply_text(
                    "⚠️ **مستوى الصوت يجب أن يكون بين 0 و 200.**"
                )
                return
        except ValueError:
            await message.reply_text(
                "⚠️ **يرجى إدخال رقم صحيح لمستوى الصوت.**"
            )
            return

        # تعيين مستوى الصوت
        success = await music_core.set_volume(chat_id, volume)

        if success:
            await set_volume(chat_id, volume)
            await message.reply_text(
                f"🔊 **تم تعيين مستوى الصوت إلى:** {volume}%"
            )
        else:
            await message.reply_text(
                "❌ **فشل في تعيين مستوى الصوت.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في تعيين الصوت:**\n`{str(e)}`"
        )


# معالجات الأزرار
@Client.on_callback_query(filters.regex("^(pause|resume|skip|stop|volume|queue)$"))
async def player_callback(client, callback_query):
    """معالجة ردود أزرار التشغيل"""
    try:
        message = callback_query.message
        chat_id = message.chat.id
        user_id = callback_query.from_user.id

        # التحقق من الصلاحيات
        member = await client.get_chat_member(chat_id, user_id)
        if member.status not in ["creator", "administrator"]:
            await callback_query.answer(
                "⚠️ هذا الأمر متاح فقط للمشرفين.",
                show_alert=True
            )
            return

        if callback_query.data == "pause":
            # إيقاف مؤقت
            success = await music_core.pause_audio(chat_id)
            if success:
                await callback_query.answer("⏸ تم إيقاف التشغيل مؤقتًا.")
            else:
                await callback_query.answer("❌ فشل في الإيقاف المؤقت.")

        elif callback_query.data == "resume":
            # استئناف
            success = await music_core.resume_audio(chat_id)
            if success:
                await callback_query.answer("▶️ تم استئناف التشغيل.")
            else:
                await callback_query.answer("❌ فشل في الاستئناف.")

        elif callback_query.data == "skip":
            # تخطي
            success = await music_core.skip_audio(chat_id)
            if success:
                await callback_query.answer("⏭ تم تخطي الأغنية الحالية.")
            else:
                await callback_query.answer("❌ فشل في التخطي.")

        elif callback_query.data == "stop":
            # إيقاف
            success = await music_core.stop_audio(chat_id)
            if success:
                await callback_query.answer("⏹ تم إيقاف التشغيل.")
            else:
                await callback_query.answer("❌ فشل في الإيقاف.")

        elif callback_query.data == "volume":
            # عرض مستوى الصوت
            current_volume = await get_volume(chat_id)
            await callback_query.answer(f"🔊 مستوى الصوت: {current_volume}%")

        elif callback_query.data == "queue":
            # عرض قائمة التشغيل
            await callback_query.answer("🎵 جاري عرض قائمة التشغيل.")

            # الحصول على قائمة التشغيل
            from plugins.music.queue import update_queue_message
            await update_queue_message(client, message, chat_id)

    except Exception as e:
        await callback_query.answer(f"❌ خطأ: {str(e)}", show_alert=True)
