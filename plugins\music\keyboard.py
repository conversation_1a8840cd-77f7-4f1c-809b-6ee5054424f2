"""
وحدة لوحة المفاتيح العربية للموسيقى
تدير واجهة لوحة المفاتيح العربية للتحكم في الموسيقى
"""

from pyrogram import Client, filters
from pyrogram.types import Message, ReplyKeyboardMarkup, KeyboardButton
from helpers.decorators import check_banned, check_force_sub, group_only
from helpers.filters import admin
from helpers.command_handler import command_handler
from database.music_db import get_current_song, get_queue, get_volume
from plugins.music.core import music_core


# لوحة المفاتيح الرئيسية للموسيقى
def get_music_keyboard():
    """الحصول على لوحة مفاتيح الموسيقى الرئيسية"""
    keyboard = [
        [
            KeyboardButton("🎵 تشغيل"),
            KeyboardButton("⏸ إيقاف مؤقت"),
            KeyboardButton("▶️ استئناف")
        ],
        [
            KeyboardButton("⏭ تخطي"),
            KeyboardButton("⏹ إيقاف"),
            KeyboardButton("🔊 الصوت")
        ],
        [
            KeyboardButton("📋 قائمة التشغيل"),
            KeyboardButton("🔄 تكرار"),
            KeyboardButton("🔀 خلط")
        ],
        [
            KeyboardButton("📊 الإحصائيات"),
            KeyboardButton("⚙️ الإعدادات"),
            KeyboardButton("🔙 الرجوع")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)


# لوحة مفاتيح الصوت
def get_volume_keyboard():
    """الحصول على لوحة مفاتيح التحكم في الصوت"""
    keyboard = [
        [
            KeyboardButton("🔇 كتم"),
            KeyboardButton("🔉 منخفض"),
            KeyboardButton("🔊 عالي")
        ],
        [
            KeyboardButton("➖ تقليل"),
            KeyboardButton("➕ زيادة"),
            KeyboardButton("🎚 مخصص")
        ],
        [
            KeyboardButton("🔙 رجوع للموسيقى")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)


# لوحة مفاتيح الإعدادات
def get_settings_keyboard():
    """الحصول على لوحة مفاتيح إعدادات الموسيقى"""
    keyboard = [
        [
            KeyboardButton("🎵 تفعيل الموسيقى"),
            KeyboardButton("🚫 تعطيل الموسيقى")
        ],
        [
            KeyboardButton("📺 تفعيل اليوتيوب"),
            KeyboardButton("🚫 تعطيل اليوتيوب")
        ],
        [
            KeyboardButton("🗑 مسح التاريخ"),
            KeyboardButton("📊 إعادة تعيين الإحصائيات")
        ],
        [
            KeyboardButton("🔙 رجوع للموسيقى")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)


@Client.on_message(command_handler(["music menu", "قائمة الموسيقى"]))
@check_banned
@check_force_sub
@group_only
async def music_menu_command(client: Client, message: Message):
    """عرض قائمة الموسيقى الرئيسية"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id
        
        # الحصول على معلومات الحالة
        current_song = await get_current_song(chat_id)
        queue = await get_queue(chat_id)
        volume = await get_volume(chat_id)
        
        # إنشاء النص
        text = "🎵 **قائمة التحكم في الموسيقى**\n\n"
        
        if current_song:
            text += f"🎧 **الآن يتم تشغيل:**\n"
            text += f"🎵 {current_song.get('title', 'غير معروف')}\n"
            text += f"👤 {current_song.get('channel', 'غير معروف')}\n\n"
        else:
            text += "🔇 **لا يتم تشغيل أي أغنية حالياً**\n\n"
        
        text += f"📋 **قائمة الانتظار:** {len(queue)} أغنية\n"
        text += f"🔊 **مستوى الصوت:** {volume}%\n\n"
        text += "استخدم الأزرار أدناه للتحكم في الموسيقى:"
        
        await message.reply_text(
            text,
            reply_markup=get_music_keyboard()
        )
        
    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في عرض القائمة:**\n`{str(e)}`"
        )


# معالجات الأزرار النصية
@Client.on_message(filters.text & filters.group)
async def handle_keyboard_buttons(client: Client, message: Message):
    """معالجة أزرار لوحة المفاتيح"""
    try:
        text = message.text
        chat_id = message.chat.id
        
        # التحقق من الصلاحيات للأوامر الحساسة
        sensitive_commands = [
            "🎵 تشغيل", "⏸ إيقاف مؤقت", "▶️ استئناف", "⏭ تخطي", 
            "⏹ إيقاف", "🔊 الصوت", "🔄 تكرار", "🔀 خلط", "⚙️ الإعدادات"
        ]
        
        if text in sensitive_commands:
            if not await admin.func(client, message):
                await message.reply_text(
                    "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
                )
                return

        # معالجة الأزرار
        if text == "🎵 تشغيل":
            await message.reply_text(
                "🎵 **لتشغيل أغنية، استخدم:**\n"
                "`تشغيل اسم الأغنية`\n\n"
                "أو أرسل رابط يوتيوب مباشرة."
            )
            
        elif text == "⏸ إيقاف مؤقت":
            success = await music_core.pause_audio(chat_id)
            if success:
                await message.reply_text("⏸ **تم إيقاف التشغيل مؤقتًا.**")
            else:
                await message.reply_text("❌ **فشل في الإيقاف المؤقت.**")
                
        elif text == "▶️ استئناف":
            success = await music_core.resume_audio(chat_id)
            if success:
                await message.reply_text("▶️ **تم استئناف التشغيل.**")
            else:
                await message.reply_text("❌ **فشل في الاستئناف.**")
                
        elif text == "⏭ تخطي":
            success = await music_core.skip_audio(chat_id)
            if success:
                await message.reply_text("⏭ **تم تخطي الأغنية الحالية.**")
            else:
                await message.reply_text("❌ **فشل في التخطي.**")
                
        elif text == "⏹ إيقاف":
            success = await music_core.stop_audio(chat_id)
            if success:
                await message.reply_text("⏹ **تم إيقاف التشغيل.**")
            else:
                await message.reply_text("❌ **فشل في الإيقاف.**")
                
        elif text == "🔊 الصوت":
            volume = await get_volume(chat_id)
            await message.reply_text(
                f"🔊 **مستوى الصوت الحالي:** {volume}%\n\n"
                "استخدم الأزرار أدناه للتحكم:",
                reply_markup=get_volume_keyboard()
            )
            
        elif text == "📋 قائمة التشغيل":
            # استدعاء دالة عرض قائمة التشغيل
            from plugins.music.queue import queue_command
            await queue_command(client, message)
            
        elif text == "⚙️ الإعدادات":
            await message.reply_text(
                "⚙️ **إعدادات الموسيقى:**\n\n"
                "استخدم الأزرار أدناه لتغيير الإعدادات:",
                reply_markup=get_settings_keyboard()
            )
            
        elif text == "🔙 الرجوع" or text == "🔙 رجوع للموسيقى":
            await message.reply_text(
                "🎵 **العودة إلى قائمة الموسيقى الرئيسية:**",
                reply_markup=get_music_keyboard()
            )
            
        # أزرار التحكم في الصوت
        elif text == "🔇 كتم":
            success = await music_core.set_volume(chat_id, 0)
            if success:
                await message.reply_text("🔇 **تم كتم الصوت.**")
            else:
                await message.reply_text("❌ **فشل في كتم الصوت.**")
                
        elif text == "🔉 منخفض":
            success = await music_core.set_volume(chat_id, 25)
            if success:
                await message.reply_text("🔉 **تم تعيين الصوت منخفض (25%).**")
            else:
                await message.reply_text("❌ **فشل في تعيين الصوت.**")
                
        elif text == "🔊 عالي":
            success = await music_core.set_volume(chat_id, 100)
            if success:
                await message.reply_text("🔊 **تم تعيين الصوت عالي (100%).**")
            else:
                await message.reply_text("❌ **فشل في تعيين الصوت.**")
                
        elif text == "➖ تقليل":
            current_volume = await get_volume(chat_id)
            new_volume = max(0, current_volume - 10)
            success = await music_core.set_volume(chat_id, new_volume)
            if success:
                await message.reply_text(f"➖ **تم تقليل الصوت إلى {new_volume}%.**")
            else:
                await message.reply_text("❌ **فشل في تقليل الصوت.**")
                
        elif text == "➕ زيادة":
            current_volume = await get_volume(chat_id)
            new_volume = min(200, current_volume + 10)
            success = await music_core.set_volume(chat_id, new_volume)
            if success:
                await message.reply_text(f"➕ **تم زيادة الصوت إلى {new_volume}%.**")
            else:
                await message.reply_text("❌ **فشل في زيادة الصوت.**")
                
        elif text == "🎚 مخصص":
            await message.reply_text(
                "🎚 **لتعيين مستوى صوت مخصص، استخدم:**\n"
                "`الصوت [الرقم]`\n\n"
                "مثال: `الصوت 75`\n"
                "القيم المسموحة: 0-200"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في معالجة الأمر:**\n`{str(e)}`"
        )
