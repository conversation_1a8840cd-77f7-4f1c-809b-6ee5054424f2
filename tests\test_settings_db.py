import asyncio
import os
import sys
import unittest


sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.redis_db import setup_redis
from database.settings_db import (
    set_bot_id, get_bot_id, get_prefix,
    get_bot_name, set_bot_name,
    get_source_name, set_source_name,
    get_developer_name, set_developer_name,
    get_channel, set_channel,
    get_group, set_group,
    is_force_sub_enabled, enable_force_sub, disable_force_sub,
    is_advanced_perms_enabled, enable_advanced_perms, disable_advanced_perms,
    is_log_enabled, enable_log, disable_log,
    get_log_channel, set_log_channel,
    is_communication_enabled, enable_communication, disable_communication,
    is_youtube_enabled, enable_youtube, disable_youtube,
    is_audio_enabled, enable_audio, disable_audio,
    get_promotion_status, set_promotion, clear_promotion,
    add_assistant_account, remove_assistant_account,
    get_assistant_account, get_all_assistant_accounts, update_assistant_account
)

class TestSettingsDB(unittest.TestCase):
    """اختبارات وظائف قاعدة بيانات الإعدادات"""

    @classmethod
    def setUpClass(cls):
        """إعداد الاختبارات"""
        
        asyncio.get_event_loop().run_until_complete(setup_redis())
        
        
        asyncio.get_event_loop().run_until_complete(set_bot_id("*********"))

    def test_bot_id(self):
        """اختبار وظائف معرف البوت"""
        
        bot_id = asyncio.get_event_loop().run_until_complete(get_bot_id())
        self.assertEqual(bot_id, "*********")

    def test_prefixes(self):
        """اختبار وظائف البادئات"""
        
        settings_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("settings"))
        force_sub_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("force_sub"))
        advanced_perms_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("advanced_perms"))
        promotion_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("promotion"))
        log_channel_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("log_channel"))
        assistant_accounts_prefix = asyncio.get_event_loop().run_until_complete(get_prefix("assistant_accounts"))
        
        
        self.assertEqual(settings_prefix, "settings:*********:")
        self.assertEqual(force_sub_prefix, "force_sub:*********")
        self.assertEqual(advanced_perms_prefix, "advanced_perms:*********")
        self.assertEqual(promotion_prefix, "promotion:*********")
        self.assertEqual(log_channel_prefix, "log_channel:*********")
        self.assertEqual(assistant_accounts_prefix, "assistant_accounts:*********")

    def test_bot_info(self):
        """اختبار وظائف معلومات البوت"""
        
        asyncio.get_event_loop().run_until_complete(set_bot_name("بوت الموسيقى"))
        bot_name = asyncio.get_event_loop().run_until_complete(get_bot_name())
        self.assertEqual(bot_name, "بوت الموسيقى")
        
        
        asyncio.get_event_loop().run_until_complete(set_source_name("سورس الموسيقى"))
        source_name = asyncio.get_event_loop().run_until_complete(get_source_name())
        self.assertEqual(source_name, "سورس الموسيقى")
        
        
        asyncio.get_event_loop().run_until_complete(set_developer_name("المطور"))
        developer_name = asyncio.get_event_loop().run_until_complete(get_developer_name())
        self.assertEqual(developer_name, "المطور")
        
        
        asyncio.get_event_loop().run_until_complete(set_channel("@channel"))
        channel = asyncio.get_event_loop().run_until_complete(get_channel())
        self.assertEqual(channel, "@channel")
        
        
        asyncio.get_event_loop().run_until_complete(set_group("@group"))
        group = asyncio.get_event_loop().run_until_complete(get_group())
        self.assertEqual(group, "@group")

    def test_force_sub(self):
        """اختبار وظائف الاشتراك الإجباري"""
        
        asyncio.get_event_loop().run_until_complete(enable_force_sub())
        force_sub_enabled = asyncio.get_event_loop().run_until_complete(is_force_sub_enabled())
        self.assertTrue(force_sub_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_force_sub())
        force_sub_enabled = asyncio.get_event_loop().run_until_complete(is_force_sub_enabled())
        self.assertFalse(force_sub_enabled)

    def test_advanced_perms(self):
        """اختبار وظائف صلاحيات التشغيل المتقدمة"""
        
        asyncio.get_event_loop().run_until_complete(enable_advanced_perms())
        advanced_perms_enabled = asyncio.get_event_loop().run_until_complete(is_advanced_perms_enabled())
        self.assertTrue(advanced_perms_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_advanced_perms())
        advanced_perms_enabled = asyncio.get_event_loop().run_until_complete(is_advanced_perms_enabled())
        self.assertFalse(advanced_perms_enabled)

    def test_log(self):
        """اختبار وظائف سجل التشغيل"""
        
        asyncio.get_event_loop().run_until_complete(enable_log())
        log_enabled = asyncio.get_event_loop().run_until_complete(is_log_enabled())
        self.assertTrue(log_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_log())
        log_enabled = asyncio.get_event_loop().run_until_complete(is_log_enabled())
        self.assertFalse(log_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(set_log_channel("-100*********"))
        log_channel = asyncio.get_event_loop().run_until_complete(get_log_channel())
        self.assertEqual(log_channel, "-100*********")

    def test_communication(self):
        """اختبار وظائف التواصل"""
        
        asyncio.get_event_loop().run_until_complete(enable_communication())
        communication_enabled = asyncio.get_event_loop().run_until_complete(is_communication_enabled())
        self.assertTrue(communication_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_communication())
        communication_enabled = asyncio.get_event_loop().run_until_complete(is_communication_enabled())
        self.assertFalse(communication_enabled)

    def test_youtube(self):
        """اختبار وظائف تشغيل اليوتيوب"""
        
        asyncio.get_event_loop().run_until_complete(enable_youtube())
        youtube_enabled = asyncio.get_event_loop().run_until_complete(is_youtube_enabled())
        self.assertTrue(youtube_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_youtube())
        youtube_enabled = asyncio.get_event_loop().run_until_complete(is_youtube_enabled())
        self.assertFalse(youtube_enabled)

    def test_audio(self):
        """اختبار وظائف التشغيل الصوتي"""
        
        asyncio.get_event_loop().run_until_complete(enable_audio())
        audio_enabled = asyncio.get_event_loop().run_until_complete(is_audio_enabled())
        self.assertTrue(audio_enabled)
        
        
        asyncio.get_event_loop().run_until_complete(disable_audio())
        audio_enabled = asyncio.get_event_loop().run_until_complete(is_audio_enabled())
        self.assertFalse(audio_enabled)

    def test_promotion(self):
        """اختبار وظائف الترويج"""
        
        asyncio.get_event_loop().run_until_complete(set_promotion("general"))
        promotion_status = asyncio.get_event_loop().run_until_complete(get_promotion_status())
        self.assertEqual(promotion_status, "general")
        
        
        asyncio.get_event_loop().run_until_complete(clear_promotion())
        promotion_status = asyncio.get_event_loop().run_until_complete(get_promotion_status())
        self.assertIsNone(promotion_status)

    def test_assistant_accounts(self):
        """اختبار وظائف الحسابات المساعدة"""
        
        asyncio.get_event_loop().run_until_complete(
            add_assistant_account("123456", "session_string", "Assistant", "assistant")
        )
        
        
        account = asyncio.get_event_loop().run_until_complete(get_assistant_account("123456"))
        self.assertEqual(account["user_id"], "123456")
        self.assertEqual(account["session_string"], "session_string")
        self.assertEqual(account["first_name"], "Assistant")
        self.assertEqual(account["username"], "assistant")
        
        
        asyncio.get_event_loop().run_until_complete(
            update_assistant_account("123456", "first_name", "New Assistant")
        )
        
        
        account = asyncio.get_event_loop().run_until_complete(get_assistant_account("123456"))
        self.assertEqual(account["first_name"], "New Assistant")
        
        
        accounts = asyncio.get_event_loop().run_until_complete(get_all_assistant_accounts())
        self.assertIn("123456", accounts)
        
        
        asyncio.get_event_loop().run_until_complete(remove_assistant_account("123456"))
        
        
        account = asyncio.get_event_loop().run_until_complete(get_assistant_account("123456"))
        self.assertIsNone(account)

if __name__ == "__main__":
    unittest.main()
