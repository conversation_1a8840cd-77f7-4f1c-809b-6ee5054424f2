from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from config import HELP_TEXT
from helpers.keyboards import help_keyboard
from helpers.decorators import check_banned, check_force_sub
from database.settings_db import get_bot_name, get_source_name
from helpers.command_handler import command_handler


@Client.on_message(command_handler("help") & filters.private)
@check_banned
@check_force_sub
async def help_command(client: Client, message: Message):
    """معالجة أمر المساعدة"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    help_text = HELP_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    
    await message.reply_text(
        help_text,
        reply_markup=await help_keyboard(),
        disable_web_page_preview=True
    )


@Client.on_callback_query(filters.regex("^commands$"))
async def commands_callback(client, callback_query):
    """معالجة زر الأوامر"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    help_text = HELP_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    
    await callback_query.edit_message_text(
        help_text,
        reply_markup=await help_keyboard(),
        disable_web_page_preview=True
    )

@Client.on_callback_query(filters.regex("^music_commands$"))
async def music_commands_callback(client, callback_query):
    """معالجة زر أوامر التشغيل"""
    
    music_text = """
🎵 **أوامر التشغيل**

• `/play` - تشغيل أغنية من يوتيوب
• `/skip` - تخطي الأغنية الحالية
• `/pause` - إيقاف التشغيل مؤقتًا
• `/resume` - استئناف التشغيل
• `/stop` - إيقاف التشغيل
• `/queue` - عرض قائمة الانتظار

⚠️ **ملاحظة:** يجب أن تكون مشرفًا في المجموعة لاستخدام هذه الأوامر.
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="commands")
        ]
    ]
    
    
    await callback_query.edit_message_text(
        music_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )

@Client.on_callback_query(filters.regex("^admin_commands$"))
async def admin_commands_callback(client, callback_query):
    """معالجة زر أوامر الإدارة"""
    
    admin_text = """
⚙️ **أوامر الإدارة**

• `/settings` - عرض إعدادات المجموعة
• `/admins` - عرض قائمة المشرفين
• `/vip` - إضافة مستخدم VIP (للمطورين فقط)
• `/unvip` - إزالة مستخدم VIP (للمطورين فقط)
• `/ban` - حظر مستخدم (للمطورين فقط)
• `/unban` - إلغاء حظر مستخدم (للمطورين فقط)

⚠️ **ملاحظة:** بعض الأوامر متاحة فقط للمطورين.
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="commands")
        ]
    ]
    
    
    await callback_query.edit_message_text(
        admin_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )

@Client.on_callback_query(filters.regex("^general_commands$"))
async def general_commands_callback(client, callback_query):
    """معالجة زر الأوامر العامة"""
    
    general_text = """
🔰 **الأوامر العامة**

• `/start` - بدء البوت
• `/help` - عرض قائمة المساعدة
• `السورس` - عرض معلومات السورس
• `مطور السورس` - عرض معلومات المطور
• `صراحة` - لعبة صراحة
• `تويت` - لعبة تويت
• `معلومة دينية` - عرض معلومة دينية

⚠️ **ملاحظة:** يمكن استخدام هذه الأوامر في الخاص أو المجموعات.
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="commands")
        ]
    ]
    
    
    await callback_query.edit_message_text(
        general_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )
