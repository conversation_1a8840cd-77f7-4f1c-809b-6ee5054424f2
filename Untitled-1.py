class userbot(object):
    def init(self, chat_id: int, userbot_obj: UserBot):
        self.chat_id = chat_id
        self.userbot = userbot_obj

    @classmethod
    async def create(cls, chat_id: int, only_userbot=False):
        userbot_obj = await ayad.getuserbot(chat_id,only_userbot)
        return cls(chat_id, userbot_obj)

    async def userbot_play(self, file):
        # await played(file, chat_id)
        LOGGER(name).info(f'=== STARTING PLAYBACK ===')
        LOGGER(name).info(f'File: {file}, Chat: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} for playback')
        return await self.userbot.play(
            self.chat_id,
            MediaStream(file),
            GroupCallConfig(auto_start=False)
        )

    async def change_stream(self, file):
        # await played(file, chat_id)
        LOGGER(name).info(f'=== CHANGING STREAM ===')
        LOGGER(name).info(f'New file: {file}, Chat: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} for stream change')

        return await self.userbot.play(
            self.chat_id,
            MediaStream(file),
            GroupCallConfig(auto_start=False)
        )

    async def record(self):
        LOGGER(name).info(f'=== STARTING CALL RECORDING ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} for recording')
        return await self.userbot.record(
            self.chat_id,
            f'record.mp3',
            GroupCallConfig(auto_start=False),
        )

    async def end_record(self, chat_id):
        LOGGER(name).info(f'=== ENDING CALL RECORDING ===')
        LOGGER(name).info(f'Chat: {chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} to end recording')
        return await self.userbot.record(self.chat_id)

    async def leave_call(self):
        LOGGER(name).info(f'=== LEAVING VOICE CALL ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} to leave call')
        try:
            del PARTICIPANT_WARNINGS[self.chat_id]
        except:
            pass
        try:
            del MUTED_WARNINGS[self.chat_id]
        except:
            pass
        try:
            clear_queue(self.chat_id)
        except:
            pass
        return await self.userbot.leave_call(self.chat_id)

    async def pause(self):
        LOGGER(name).info(f'=== PAUSING PLAYBACK ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        return await self.userbot.pause(self.chat_id)

    async def resume(self, chat_id):
        LOGGER(name).info(f'=== RESUMING PLAYBACK ===')
        LOGGER(name).info(f'Chat: {chat_id}')
        return await self.userbot.resume(self.chat_id)

    async def mute(self):
        LOGGER(name).info(f'=== MUTING AUDIO ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        return await self.userbot.mute(self.chat_id)

    async def unmute(self):
        LOGGER(name).info(f'=== UNMUTING AUDIO ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        return await self.userbot.unmute(self.chat_id)

    async def leave_chat(self):
        LOGGER(name).info(f'=== LEAVING CHAT ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} to leave chat')
        return await self.userbot.ay.leave_chat(self.chat_id)

    async def join_chat(self):
        LOGGER(name).info(f'=== JOINING CHAT ===')
        LOGGER(name).info(f'Target: {self.chat_id}')
        LOGGER(name).info(f'Using userbot #{self.userbot.num} to join chat')
        chat = await Bot.get_chat(self.chat_id)
        if chat.username:
            invite = chat.username
        elif chat.invite_link:
            invite = chat.invite_link
        else:
            invite = await Bot.export_chat_invite_link(chat.id)
        return await self.userbot.ay.join_chat(invite)


async def number_users_in_call(self):
        LOGGER(name).info(f'=== COUNTING CALL PARTICIPANTS ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        return len(await self.userbot.get_participants(self.chat_id))

    async def skip(self, num=1):
        LOGGER(name).info(f'=== SKIPPING TRACKS ===')
        LOGGER(name).info(f'Chat: {self.chat_id}, Skip count: {num}')
        chat_queue = get_queue(self.chat_id)
        if len(chat_queue) <= num:
            await self.leave_call()
            return 1
        else:
            await self.change_stream(chat_queue[num][0])
            photo = None
            try:
                photo = chat_queue[num][2]
                caption = chat_queue[num][3]
                markup = chat_queue[num][4]
            except:
                pass
            pop_an_item(self.chat_id)
            if photo:
                await Bot.send_photo(self.chat_id, photo, caption, reply_markup=markup)
            return 2

    async def get_call_is_playing(self):
        LOGGER(name).debug(f'Checking if call is active in chat {self.chat_id}')

        try:
            calls = await self.userbot.calls

            if int(self.chat_id) in calls:
                info = calls[int(self.chat_id)]
                is_active = info.capture == Call.Status.ACTIVE
                LOGGER(name).debug(f'Call status in chat {self.chat_id}: {"Active" if is_active else "Inactive"}')
                return is_active
            else:
                LOGGER(name).debug(f'No call found in chat {self.chat_id}')
                return False
        except Exception as e:
            LOGGER(name).error(f'Error checking call status in chat {self.chat_id}: {e}')
            return False

    async def get_participants_on_the_call(self):
        LOGGER(name).info(f'=== GETTING CALL PARTICIPANTS ===')
        LOGGER(name).info(f'Chat: {self.chat_id}')
        if not await self.get_call_is_playing():
            await self.userbot.play(int(self.chat_id))
            # await sleep(0.1)
            participants = await self.userbot.get_participants(int(self.chat_id))
            # await sleep(0.3)
            await self.userbot.leave_call(int(self.chat_id))
        else:
            participants = await self.userbot.get_participants(int(self.chat_id))

        LOGGER(name).info(f'participants list in {self.chat_id}\n{participants}')
        return participants