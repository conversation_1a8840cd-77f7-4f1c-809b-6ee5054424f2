



تم تحسين وظيفة رفع الصور في واجهة إدارة المساعد لتكون أكثر موثوقية وفعالية من خلال استخدام التحميل المحلي بدلاً من الاعتماد على معرفات الملفات أو الروابط المباشرة.




- **المشكلة السابقة**: استخدام `message.photo.file_id` مباشرة مع `set_profile_photo()`
- **السبب**: معرفات الملفات قد لا تعمل بشكل موثوق مع حسابات المساعد
- **النتيجة**: فشل في تحديث الصورة الشخصية في كثير من الأحيان


- **النهج الجديد**: تحميل الصورة محلياً أولاً ثم استخدام المسار المحلي
- **الفائدة**: موثوقية أعلى وتوافق أفضل مع API التليجرام
- **النتيجة**: نجاح أكبر في تحديث الصور الشخصية







```python
async def handle_photo_input(client: Client, message: Message):
    """معالجة رفع صورة جديدة مع التحميل المحلي"""
```

**الخطوات الجديدة:**
1. **التحقق من الصورة**: التأكد من وجود صورة في الرسالة
2. **إنشاء مجلد مؤقت**: إنشاء مجلد `temp_photos` إذا لم يكن موجوداً
3. **تحميل الصورة**: تحميل الصورة من تليجرام إلى ملف محلي
4. **تحديث الصورة**: استخدام المسار المحلي مع `set_profile_photo()`
5. **تنظيف الملفات**: حذف الملف المؤقت بعد الانتهاء

**الميزات الجديدة:**
- ✅ **رسائل حالة تفاعلية**: إعلام المستخدم بتقدم العملية
- ✅ **معالجة أخطاء محسنة**: رسائل خطأ واضحة ومفيدة
- ✅ **تنظيف تلقائي**: حذف الملفات المؤقتة تلقائياً
- ✅ **واجهة عربية**: جميع الرسائل باللغة العربية





```python
async def handle_photo_url_input(client: Client, message: Message):
    """معالجة رابط الصورة مع التحميل المحلي"""
```

**الخطوات الجديدة:**
1. **التحقق من الرابط**: التأكد من صحة الرابط
2. **التحقق من المكتبات**: التأكد من توفر `aiohttp` و `aiofiles`
3. **تحميل الصورة**: تحميل الصورة من الرابط إلى ملف محلي
4. **التحقق من النوع**: التأكد من أن المحتوى صورة صحيحة
5. **تحديث الصورة**: استخدام المسار المحلي مع `set_profile_photo()`
6. **تنظيف الملفات**: حذف الملف المؤقت بعد الانتهاء

**المتطلبات الإضافية:**
- `aiohttp`: لتحميل الصور من الروابط
- `aiofiles`: للكتابة غير المتزامنة للملفات




```python
def ensure_temp_photos_dir():
    """إنشاء مجلد الصور المؤقت إذا لم يكن موجوداً"""
    temp_dir = "temp_photos"
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    return temp_dir
```


```python
def check_url_download_dependencies():
    """التحقق من توفر مكتبات تحميل الصور من الروابط"""
    try:
        import aiohttp
        import aiofiles
        return True
    except ImportError:
        return False
```




```python

temp_file_path = os.path.join(temp_dir, f"profile_photo_{user_id}_{message.id}.jpg")


temp_file_path = os.path.join(temp_dir, f"profile_photo_url_{user_id}_{message.id}.{file_extension}")
```


```python

status_message = await message.reply_text(
    "🔄 **جاري تحميل الصورة...**\n\n"
    "⏳ **يرجى الانتظار...**"
)


await status_message.edit_text(
    "📥 **جاري تحميل الصورة من تليجرام...**\n\n"
    "⏳ **يرجى الانتظار...**"
)


await status_message.edit_text(
    "🖼 **جاري تحديث الصورة الشخصية للمساعد...**\n\n"
    "⏳ **يرجى الانتظار...**"
)
```


```python
except Exception as e:
    await message.reply_text(
        f"❌ **حدث خطأ أثناء تحديث الصورة:**\n\n"
        f"`{str(e)}`\n\n"
        f"💡 **نصائح لحل المشكلة:**\n"
        f"• تأكد من أن الصورة بصيغة صحيحة (JPG, PNG)\n"
        f"• تأكد من أن حجم الصورة مناسب (أقل من 10 ميجابايت)\n"
        f"• تأكد من أن الحساب المساعد متصل بشكل صحيح",
        reply_markup=await assistant_photo_keyboard()
    )
```


```python
finally:
    
    if temp_file_path and os.path.exists(temp_file_path):
        try:
            os.remove(temp_file_path)
        except Exception as cleanup_error:
            print(f"تحذير: فشل في حذف الملف المؤقت {temp_file_path}: {cleanup_error}")
```




- ✅ **نجاح أكبر**: تحديث الصور يعمل بشكل أكثر موثوقية
- ✅ **توافق أفضل**: يعمل مع جميع أنواع الصور المدعومة
- ✅ **استقرار محسن**: أقل عرضة للفشل بسبب مشاكل الشبكة


- ✅ **ردود فعل فورية**: رسائل حالة تفاعلية
- ✅ **رسائل خطأ واضحة**: نصائح مفيدة لحل المشاكل
- ✅ **واجهة عربية**: جميع الرسائل باللغة العربية


- ✅ **تنظيف تلقائي**: حذف الملفات المؤقتة تلقائياً
- ✅ **استخدام ذاكرة محسن**: تحميل الصور بقطع صغيرة
- ✅ **منع تراكم الملفات**: تنظيف فوري بعد كل عملية


- ✅ **التحقق من النوع**: التأكد من أن الملفات صور صحيحة
- ✅ **التحقق من الحجم**: منع تحميل ملفات كبيرة جداً
- ✅ **عزل الملفات**: استخدام مجلد مؤقت منفصل




- `os`: إدارة الملفات والمجلدات
- `pyrogram`: التفاعل مع API التليجرام


- `aiohttp`: تحميل الصور من الروابط
- `aiofiles`: كتابة الملفات غير المتزامنة


```bash
pip install aiohttp aiofiles
```




1. اختر "📷 رفع صورة جديدة"
2. أرسل الصورة المطلوبة
3. انتظر رسائل الحالة
4. تأكيد النجاح أو معالجة الخطأ


1. اختر "🔗 إضافة صورة برابط"
2. أرسل رابط الصورة
3. انتظر التحميل والتحديث
4. تأكيد النجاح أو معالجة الخطأ



التحسينات الجديدة تجعل وظيفة رفع الصور:
- ✅ **أكثر موثوقية** من خلال التحميل المحلي
- ✅ **أسهل في الاستخدام** مع رسائل الحالة التفاعلية
- ✅ **أكثر أماناً** مع التحقق من الملفات
- ✅ **أفضل في إدارة الموارد** مع التنظيف التلقائي

هذه التحسينات تضمن تجربة مستخدم ممتازة وموثوقية عالية في تحديث الصور الشخصية للحساب المساعد.
