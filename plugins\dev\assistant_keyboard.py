from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler


from plugins.dev.assistant_keyboard_interface import assistant_management_start


@Client.on_message(command_handler(["assistant keyboard", "كيبورد المساعد", "لوحة المساعد"]) & filters.private & dev)
@dev_only
async def assistant_keyboard_command(client: Client, message: Message):
    """معالجة أمر إدارة المساعد بالكيبورد - إعادة توجيه للواجهة الجديدة"""
    await assistant_management_start(client, message)


@Client.on_message(command_handler(["امساعد", "مساعد كيبورد"]) & filters.private & dev)
@dev_only
async def assistant_keyboard_short_command(client: Client, message: Message):
    """معالجة أمر إدارة المساعد المختصر"""
    await assistant_management_start(client, message)


@Client.on_message(command_handler(["assistant", "مساعد"]) & filters.private & dev)
@dev_only
async def assistant_quick_command(client: Client, message: Message):
    """أمر سريع لإدارة المساعد"""
    await assistant_management_start(client, message)
