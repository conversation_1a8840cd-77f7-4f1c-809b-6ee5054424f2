import os
import time
import importlib
import logging
from pyrogram import Client, idle
from pyromod import listen
from config import API_ID, API_HASH, BOT_TOKEN


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
LOGGER = logging.getLogger(__name__)


class MusicBot(Client):
    def __init__(self):
        super().__init__(
            name="MusicBot",
            api_id=API_ID,
            api_hash=API_HASH,
            bot_token=BOT_TOKEN,
            plugins={"root": "plugins"}
        )
        self.LOGGER = LOGGER

    async def start(self):
        await super().start()
        self.start_time = time.time()  # حفظ وقت بدء التشغيل
        self.LOGGER.info("تم تشغيل البوت!")


        from database.redis_db import setup_redis
        await setup_redis()


        from helpers.keyboards import setup_keyboards
        setup_keyboards()


        bot_info = await self.get_me()
        self.LOGGER.info(f"اسم البوت: {bot_info.first_name}")
        self.LOGGER.info(f"معرف البوت: @{bot_info.username}")
        self.LOGGER.info(f"معرف البوت: {bot_info.id}")


        from database.settings_db import set_bot_id
        await set_bot_id(bot_info.id)

        
        self.assistant = None
        from database.settings_db import get_all_assistant_accounts
        assistants = await get_all_assistant_accounts()
        
        if assistants:
            user_id = list(assistants.keys())[0]
            account = assistants[user_id]
            try:
                self.assistant = Client(
                    f"assistant_session",
                    api_id=API_ID,
                    api_hash=API_HASH,
                    session_string=account.get('session_string')
                )
                await self.assistant.start()
                self.LOGGER.info(f"تم تشغيل الحساب المساعد: {account.get('first_name')} ({user_id})")
            except Exception as e:
                self.LOGGER.error(f"فشل تشغيل الحساب المساعد {user_id}: {str(e)}")

        
        self._load_all_modules()

    def _load_all_modules(self):
        """تحميل جميع الوحدات من المجلدات"""
        for folder in ["plugins/middleware", "plugins/dev", "plugins/admin", "plugins/user", "plugins/music"]:
            if not os.path.exists(folder):
                os.makedirs(folder)

            for module_path in os.listdir(folder):
                if module_path.endswith(".py"):
                    module_name = module_path[:-3]
                    importlib.import_module(f"{folder.replace('/', '.')}.{module_name}")
                    self.LOGGER.info(f"تم تحميل الوحدة: {folder}/{module_name}")

    async def stop(self, *args):
        await super().stop()
        self.LOGGER.info("تم إيقاف البوت!")


if __name__ == "__main__":
    bot = MusicBot()
    bot.run()
