from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from helpers.command_handler import command_handler
from helpers.middleware import not_banned_or_muted
from database.settings_db import get_bot_name


@Client.on_message(command_handler(["الاوامر", "الأوامر", "commands"]))
async def commands_menu(client: Client, message: Message):
    """عرض قائمة الأوامر التفاعلية"""

    # التحقق من الحظر والكتم
    from database.users_db import is_user_banned, is_developer
    from database.redis_db import redis_get

    if message.from_user:
        # تجاهل فحص المطورين
        if not await is_developer(message.from_user.id):
            # فحص الحظر
            if await is_user_banned(message.from_user.id):
                return

            # فحص الكتم (في المجموعات فقط)
            if message.chat.type in ["group", "supergroup"]:
                mute_key = f"muted_user:{message.from_user.id}"
                if await redis_get(mute_key):
                    try:
                        await message.delete()
                    except:
                        pass
                    return

    bot_name = await get_bot_name()
    
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🎵 أوامر الموسيقى", callback_data="commands_music"),
            InlineKeyboardButton("👑 أوامر الإدارة", callback_data="commands_admin")
        ],
        [
            InlineKeyboardButton("⚙️ أوامر الإعدادات", callback_data="commands_settings"),
            InlineKeyboardButton("🎮 أوامر الترفيه", callback_data="commands_fun")
        ],
        [
            InlineKeyboardButton("📊 أوامر الإحصائيات", callback_data="commands_stats"),
            InlineKeyboardButton("🔧 أوامر المطور", callback_data="commands_dev")
        ],
        [
            InlineKeyboardButton("ℹ️ معلومات عامة", callback_data="commands_general")
        ]
    ])
    
    await message.reply_text(
        f"📋 **قائمة أوامر {bot_name}**\n\n"
        "اختر الفئة التي تريد عرض أوامرها:",
        reply_markup=keyboard
    )


@Client.on_callback_query(filters.regex("^commands_"))
async def commands_callback(client: Client, callback_query: CallbackQuery):
    """معالجة أزرار قائمة الأوامر"""
    data = callback_query.data
    bot_name = await get_bot_name()
    
    if data == "commands_music":
        text = f"""
🎵 **أوامر الموسيقى - {bot_name}**

▫️ `شغل` أو `play` - تشغيل أغنية من يوتيوب
▫️ `تخطي` أو `skip` - تخطي الأغنية الحالية
▫️ `ايقاف` أو `pause` - إيقاف التشغيل مؤقتًا
▫️ `استئناف` أو `resume` - استئناف التشغيل
▫️ `توقف` أو `stop` - إيقاف التشغيل نهائيًا
▫️ `قائمة الانتظار` أو `queue` - عرض قائمة الانتظار
▫️ `تحميل` أو `download` - تحميل أغنية

**مثال:** `شغل اسم الأغنية`
        """
    
    elif data == "commands_admin":
        text = f"""
👑 **أوامر الإدارة - {bot_name}**

**🚫 أوامر الحظر والكتم:**
▫️ `حظر` - حظر مستخدم
▫️ `الغاء حظر` - إلغاء حظر مستخدم
▫️ `كتم` - كتم مستخدم
▫️ `الغاء كتم` - إلغاء كتم مستخدم

**⭐ أوامر الرتب:**
▫️ `رفع vip` - رفع مستخدم إلى VIP
▫️ `تنزيل vip` - تنزيل مستخدم من VIP
▫️ `رفع مطور` - رفع مستخدم إلى مطور
▫️ `رفع ادمن` - رفع مستخدم إلى أدمن
▫️ `رفع مشرف` - رفع مستخدم إلى مشرف في المجموعة

**📋 أوامر القوائم:**
▫️ `قائمة المحظورين` - عرض المستخدمين المحظورين
▫️ `قائمة المكتومين` - عرض المستخدمين المكتومين
        """
    
    elif data == "commands_settings":
        text = f"""
⚙️ **أوامر الإعدادات - {bot_name}**

▫️ `الاعدادات` أو `settings` - لوحة إعدادات البوت
▫️ `تغيير اسم البوت` - تغيير اسم البوت
▫️ `تغيير اسم السورس` - تغيير اسم السورس
▫️ `تعيين قناة` - تعيين قناة البوت
▫️ `تعيين مجموعة` - تعيين مجموعة البوت
▫️ `تفعيل الاشتراك الاجباري` - تفعيل الاشتراك الإجباري
▫️ `تعطيل الاشتراك الاجباري` - تعطيل الاشتراك الإجباري

**📱 إدارة المساعد:**
▫️ `مساعد` أو `assistant` - لوحة إدارة المساعد
        """
    
    elif data == "commands_fun":
        text = f"""
🎮 **أوامر الترفيه - {bot_name}**

▫️ `معلوماتي` - عرض معلوماتك
▫️ `معلومات` - عرض معلومات مستخدم
▫️ `صورتي` - عرض صورة ملفك الشخصي
▫️ `ايدي` أو `id` - عرض معرف المستخدم أو المجموعة

**🎲 ألعاب:**
▫️ `نرد` - رمي النرد
▫️ `عملة` - رمي العملة
▫️ `سهم` - رمي السهم
        """
    
    elif data == "commands_stats":
        text = f"""
📊 **أوامر الإحصائيات - {bot_name}**

▫️ `الاحصائيات` أو `stats` - إحصائيات البوت
▫️ `عدد المستخدمين` - عدد مستخدمي البوت
▫️ `عدد المجموعات` - عدد مجموعات البوت
▫️ `معلومات الخادم` - معلومات الخادم
▫️ `استخدام الذاكرة` - استخدام ذاكرة الخادم

**📈 إحصائيات متقدمة:**
▫️ `احصائيات التشغيل` - إحصائيات تشغيل الموسيقى
▫️ `الاغاني الاكثر تشغيلا` - الأغاني الأكثر تشغيلاً
        """
    
    elif data == "commands_dev":
        text = f"""
🔧 **أوامر المطور - {bot_name}**

**📡 أوامر البث:**
▫️ `بث` أو `broadcast` - بث رسالة لجميع المستخدمين
▫️ `بث للمجموعات` - بث رسالة لجميع المجموعات

**🔄 أوامر النظام:**
▫️ `اعادة تشغيل` أو `restart` - إعادة تشغيل البوت
▫️ `ايقاف` أو `shutdown` - إيقاف البوت
▫️ `تحديث` أو `update` - تحديث البوت

**📝 أوامر السجلات:**
▫️ `السجلات` أو `logs` - عرض سجلات البوت
▫️ `مسح السجلات` - مسح سجلات البوت

**⚠️ ملاحظة:** هذه الأوامر متاحة للمطورين فقط
        """
    
    elif data == "commands_general":
        text = f"""
ℹ️ **معلومات عامة - {bot_name}**

**🔍 كيفية استخدام الأوامر:**
• يمكن كتابة الأوامر باللغة العربية أو الإنجليزية
• بعض الأوامر تدعم الرد على الرسائل
• يمكن استخدام معرف المستخدم أو اسم المستخدم

**📝 أمثلة:**
• `شغل اسم الأغنية`
• `حظر @username`
• `كتم 123456789 30د`

**⏰ وحدات الوقت للكتم:**
• `د` = دقائق (مثال: 30د)
• `س` = ساعات (مثال: 2س)
• `ي` = أيام (مثال: 7ي)

**🆘 للمساعدة:**
• استخدم أمر `مساعدة` أو `help`
• تواصل مع المطور عبر البوت
        """
    
    # إنشاء زر العودة
    back_keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="commands_back")]
    ])
    
    await callback_query.edit_message_text(
        text,
        reply_markup=back_keyboard
    )


@Client.on_callback_query(filters.regex("^commands_back$"))
async def commands_back(client: Client, callback_query: CallbackQuery):
    """العودة للقائمة الرئيسية"""
    bot_name = await get_bot_name()
    
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🎵 أوامر الموسيقى", callback_data="commands_music"),
            InlineKeyboardButton("👑 أوامر الإدارة", callback_data="commands_admin")
        ],
        [
            InlineKeyboardButton("⚙️ أوامر الإعدادات", callback_data="commands_settings"),
            InlineKeyboardButton("🎮 أوامر الترفيه", callback_data="commands_fun")
        ],
        [
            InlineKeyboardButton("📊 أوامر الإحصائيات", callback_data="commands_stats"),
            InlineKeyboardButton("🔧 أوامر المطور", callback_data="commands_dev")
        ],
        [
            InlineKeyboardButton("ℹ️ معلومات عامة", callback_data="commands_general")
        ]
    ])
    
    await callback_query.edit_message_text(
        f"📋 **قائمة أوامر {bot_name}**\n\n"
        "اختر الفئة التي تريد عرض أوامرها:",
        reply_markup=keyboard
    )
