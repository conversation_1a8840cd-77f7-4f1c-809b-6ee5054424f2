from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from config import START_TEXT, OWNER_ID
from helpers.keyboards import start_keyboard
from helpers.decorators import check_banned, check_force_sub
from database.users_db import add_user
from database.settings_db import get_bot_name, get_source_name
from utils.logger import log_new_user
from helpers.command_handler import command_handler


@Client.on_message(command_handler("start") & filters.private)
@check_banned
@check_force_sub
async def start_command(client: Client, message: Message):
    """معالجة أمر البداية"""
    
    user_id = message.from_user.id
    username = message.from_user.username
    first_name = message.from_user.first_name
    
    await add_user(user_id, username, first_name)
    
    
    await log_new_user(client, user_id, username, first_name)
    
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    start_text = START_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    
    await message.reply_text(
        start_text,
        reply_markup=await start_keyboard(),
        disable_web_page_preview=True
    )


@Client.on_message(command_handler("السورس"))
@check_banned
@check_force_sub
async def source_command(client: Client, message: Message):
    """معالجة أمر السورس"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    source_text = f"""
🔰 **{source_name}**

🤖 **اسم البوت:** {bot_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **سورس ميوزك متكامل يدعم تشغيل الصوتيات في المكالمات**
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    
    
    await message.reply_text(
        source_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )


@Client.on_message(filters.regex("^مطور السورس$"))
async def dev_command(client: Client, message: Message):
    """معالجة أمر المطور"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    dev_text = f"""
👨‍💻 **مطور {source_name}**

🤖 **اسم البوت:** {bot_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **للتواصل مع المطور اضغط على الزر أدناه**
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ]
    ]
    
    
    await message.reply_text(
        dev_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )


@Client.on_callback_query(filters.regex("^back_to_start$"))
async def back_to_start(client, callback_query):
    """معالجة زر الرجوع إلى البداية"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    start_text = START_TEXT.format(
        bot_name=bot_name,
        source_name=source_name
    )
    
    
    await callback_query.edit_message_text(
        start_text,
        reply_markup=await start_keyboard(),
        disable_web_page_preview=True
    )

@Client.on_callback_query(filters.regex("^about$"))
async def about_callback(client, callback_query):
    """معالجة زر حول البوت"""
    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    
    
    about_text = f"""
ℹ️ **معلومات عن {bot_name}**

🤖 **اسم البوت:** {bot_name}
🔰 **السورس:** {source_name}
👨‍💻 **المطور:** [المطور](tg://user?id={OWNER_ID})

✨ **بوت تشغيل موسيقى في المكالمات الصوتية**
🎵 **يدعم تشغيل الموسيقى من يوتيوب والملفات الصوتية**
    """
    
    
    buttons = [
        [
            InlineKeyboardButton("👨‍💻 المطور", url=f"tg://user?id={OWNER_ID}")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    
    
    await callback_query.edit_message_text(
        about_text,
        reply_markup=InlineKeyboardMarkup(buttons),
        disable_web_page_preview=True
    )
