"""
وحدة إدارة قائمة التشغيل
تدير قوائم الانتظار والتشغيل للموسيقى
"""

from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from helpers.decorators import check_banned, check_force_sub, group_only
from helpers.filters import admin
from helpers.command_handler import command_handler
from database.music_db import (
    get_queue, add_to_queue, clear_queue, get_queue_length,
    get_current_song, set_repeat_mode, get_repeat_mode,
    set_shuffle_mode, get_shuffle_mode
)


@Client.on_message(command_handler(["queue", "قائمة التشغيل", "قائمة الانتظار"]))
@check_banned
@check_force_sub
@group_only
async def queue_command(client: Client, message: Message):
    """عرض قائمة التشغيل"""
    try:
        chat_id = message.chat.id

        # الحصول على الأغنية الحالية
        current_song = await get_current_song(chat_id)

        # الحصول على قائمة الانتظار
        queue = await get_queue(chat_id)
        queue_length = len(queue)

        # إنشاء النص
        text = "🎵 **قائمة التشغيل**\n\n"

        # الأغنية الحالية
        if current_song:
            text += f"🎧 **الآن يتم تشغيل:**\n"
            text += f"🎵 {current_song.get('title', 'غير معروف')}\n"
            text += f"👤 {current_song.get('channel', 'غير معروف')}\n"
            text += f"⏱ {current_song.get('duration', '0:00')}\n\n"
        else:
            text += "🔇 **لا يتم تشغيل أي أغنية حالياً**\n\n"

        # قائمة الانتظار
        if queue_length > 0:
            text += f"📋 **قائمة الانتظار ({queue_length} أغنية):**\n\n"

            for i, song in enumerate(queue[:10], 1):  # عرض أول 10 أغاني فقط
                text += f"{i}. {song.get('title', 'غير معروف')}\n"
                text += f"   👤 {song.get('channel', 'غير معروف')} | ⏱ {song.get('duration', '0:00')}\n\n"

            if queue_length > 10:
                text += f"... و {queue_length - 10} أغنية أخرى\n\n"
        else:
            text += "📋 **قائمة الانتظار فارغة**\n\n"

        # إعدادات التشغيل
        repeat_mode = await get_repeat_mode(chat_id)
        shuffle_mode = await get_shuffle_mode(chat_id)

        text += "⚙️ **الإعدادات:**\n"
        text += f"🔄 التكرار: {get_repeat_text(repeat_mode)}\n"
        text += f"🔀 الخلط: {'مفعل' if shuffle_mode else 'معطل'}"

        # الأزرار
        buttons = [
            [
                InlineKeyboardButton("🔄 تكرار", callback_data="toggle_repeat"),
                InlineKeyboardButton("🔀 خلط", callback_data="toggle_shuffle")
            ],
            [
                InlineKeyboardButton("🗑 مسح القائمة", callback_data="clear_queue"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh_queue")
            ]
        ]

        await message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(buttons)
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في عرض قائمة التشغيل:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["clear queue", "مسح القائمة", "مسح قائمة التشغيل"]))
@check_banned
@check_force_sub
@group_only
async def clear_queue_command(client: Client, message: Message):
    """مسح قائمة التشغيل"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # مسح القائمة
        success = await clear_queue(chat_id)

        if success:
            await message.reply_text(
                "🗑 **تم مسح قائمة التشغيل بنجاح.**"
            )
        else:
            await message.reply_text(
                "❌ **فشل في مسح قائمة التشغيل.**"
            )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في مسح القائمة:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["repeat", "تكرار"]))
@check_banned
@check_force_sub
@group_only
async def repeat_command(client: Client, message: Message):
    """تغيير وضع التكرار"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # الحصول على الوضع الحالي
        current_mode = await get_repeat_mode(chat_id)

        # تغيير الوضع
        if current_mode == "off":
            new_mode = "single"
        elif current_mode == "single":
            new_mode = "all"
        else:
            new_mode = "off"

        # تعيين الوضع الجديد
        await set_repeat_mode(chat_id, new_mode)

        await message.reply_text(
            f"🔄 **تم تغيير وضع التكرار إلى:** {get_repeat_text(new_mode)}"
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في تغيير وضع التكرار:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["shuffle", "خلط"]))
@check_banned
@check_force_sub
@group_only
async def shuffle_command(client: Client, message: Message):
    """تفعيل/تعطيل وضع الخلط"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        chat_id = message.chat.id

        # الحصول على الوضع الحالي
        current_mode = await get_shuffle_mode(chat_id)

        # تغيير الوضع
        new_mode = not current_mode
        await set_shuffle_mode(chat_id, new_mode)

        status = "مفعل" if new_mode else "معطل"
        await message.reply_text(
            f"🔀 **وضع الخلط:** {status}"
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في تغيير وضع الخلط:**\n`{str(e)}`"
        )


# معالجات الأزرار
@Client.on_callback_query(filters.regex("^(toggle_repeat|toggle_shuffle|clear_queue|refresh_queue)$"))
async def queue_callback(client, callback_query):
    """معالجة أزرار قائمة التشغيل"""
    try:
        message = callback_query.message
        chat_id = message.chat.id
        user_id = callback_query.from_user.id

        # التحقق من الصلاحيات
        member = await client.get_chat_member(chat_id, user_id)
        if member.status not in ["creator", "administrator"]:
            await callback_query.answer(
                "⚠️ هذا الأمر متاح فقط للمشرفين.",
                show_alert=True
            )
            return

        if callback_query.data == "toggle_repeat":
            # تغيير وضع التكرار
            current_mode = await get_repeat_mode(chat_id)
            if current_mode == "off":
                new_mode = "single"
            elif current_mode == "single":
                new_mode = "all"
            else:
                new_mode = "off"

            await set_repeat_mode(chat_id, new_mode)
            await callback_query.answer(f"🔄 وضع التكرار: {get_repeat_text(new_mode)}")

        elif callback_query.data == "toggle_shuffle":
            # تغيير وضع الخلط
            current_mode = await get_shuffle_mode(chat_id)
            new_mode = not current_mode
            await set_shuffle_mode(chat_id, new_mode)

            status = "مفعل" if new_mode else "معطل"
            await callback_query.answer(f"🔀 وضع الخلط: {status}")

        elif callback_query.data == "clear_queue":
            # مسح القائمة
            success = await clear_queue(chat_id)
            if success:
                await callback_query.answer("🗑 تم مسح قائمة التشغيل.")
            else:
                await callback_query.answer("❌ فشل في مسح القائمة.")

        elif callback_query.data == "refresh_queue":
            # تحديث العرض
            await callback_query.answer("🔄 تم تحديث القائمة.")

        # تحديث الرسالة
        await update_queue_message(client, message, chat_id)

    except Exception as e:
        await callback_query.answer(f"❌ خطأ: {str(e)}", show_alert=True)


async def update_queue_message(client: Client, message: Message, chat_id: int):
    """تحديث رسالة قائمة التشغيل"""
    try:
        # الحصول على الأغنية الحالية
        current_song = await get_current_song(chat_id)

        # الحصول على قائمة الانتظار
        queue = await get_queue(chat_id)
        queue_length = len(queue)

        # إنشاء النص
        text = "🎵 **قائمة التشغيل**\n\n"

        # الأغنية الحالية
        if current_song:
            text += f"🎧 **الآن يتم تشغيل:**\n"
            text += f"🎵 {current_song.get('title', 'غير معروف')}\n"
            text += f"👤 {current_song.get('channel', 'غير معروف')}\n"
            text += f"⏱ {current_song.get('duration', '0:00')}\n\n"
        else:
            text += "🔇 **لا يتم تشغيل أي أغنية حالياً**\n\n"

        # قائمة الانتظار
        if queue_length > 0:
            text += f"📋 **قائمة الانتظار ({queue_length} أغنية):**\n\n"

            for i, song in enumerate(queue[:10], 1):
                text += f"{i}. {song.get('title', 'غير معروف')}\n"
                text += f"   👤 {song.get('channel', 'غير معروف')} | ⏱ {song.get('duration', '0:00')}\n\n"

            if queue_length > 10:
                text += f"... و {queue_length - 10} أغنية أخرى\n\n"
        else:
            text += "📋 **قائمة الانتظار فارغة**\n\n"

        # إعدادات التشغيل
        repeat_mode = await get_repeat_mode(chat_id)
        shuffle_mode = await get_shuffle_mode(chat_id)

        text += "⚙️ **الإعدادات:**\n"
        text += f"🔄 التكرار: {get_repeat_text(repeat_mode)}\n"
        text += f"🔀 الخلط: {'مفعل' if shuffle_mode else 'معطل'}"

        # الأزرار
        buttons = [
            [
                InlineKeyboardButton("🔄 تكرار", callback_data="toggle_repeat"),
                InlineKeyboardButton("🔀 خلط", callback_data="toggle_shuffle")
            ],
            [
                InlineKeyboardButton("🗑 مسح القائمة", callback_data="clear_queue"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh_queue")
            ]
        ]

        await message.edit_text(
            text,
            reply_markup=InlineKeyboardMarkup(buttons)
        )

    except Exception as e:
        print(f"خطأ في تحديث رسالة القائمة: {e}")


def get_repeat_text(mode: str) -> str:
    """الحصول على نص وضع التكرار"""
    if mode == "off":
        return "معطل"
    elif mode == "single":
        return "تكرار الأغنية الحالية"
    elif mode == "all":
        return "تكرار القائمة"
    else:
        return "غير معروف"
