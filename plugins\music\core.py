"""
وحدة النواة للموسيقى
تدير pytgcalls والمكالمات الصوتية
"""

import asyncio
import os
from typing import Dict, Optional, Any
from pytgcalls import PyTgCalls, StreamType
from pytgcalls.types.input_stream import AudioPiped, AudioVideoPiped
from pytgcalls.types.input_stream.quality import HighQualityAudio, HighQualityVideo
from pytgcalls.exceptions import NoActiveGroupCall, NotInGroupCallError, AlreadyJoinedError
from pyrogram import Client
from pyrogram.errors import ChatAdminRequired, UserAlreadyParticipant

from database.music_db import (
    set_current_song, get_current_song, clear_current_song,
    set_playback_status, get_playback_status,
    get_next_song, get_volume, set_volume
)
from plugins.music.logger import (
    handle_music_errors, log_music_action, monitor_performance,
    music_logger, error_handler
)


class MusicCore:
    """فئة النواة لإدارة الموسيقى"""
    
    def __init__(self):
        self.pytgcalls: Optional[PyTgCalls] = None
        self.client: Optional[Client] = None
        self.assistant: Optional[Client] = None
        self.active_calls: Dict[int, bool] = {}
        self.call_handlers = {}
    
    @handle_music_errors
    @monitor_performance("initialize_music_core")
    async def initialize(self, client: Client, assistant: Optional[Client] = None):
        """تهيئة النواة"""
        try:
            self.client = client
            self.assistant = assistant or client

            # إنشاء pytgcalls
            self.pytgcalls = PyTgCalls(self.assistant)

            # تسجيل معالجات الأحداث
            self.pytgcalls.on_stream_end()(self._on_stream_end)
            self.pytgcalls.on_kicked()(self._on_kicked)
            self.pytgcalls.on_closed_voice_chat()(self._on_closed_voice_chat)

            # بدء pytgcalls
            await self.pytgcalls.start()

            music_logger.info("تم تهيئة نواة الموسيقى بنجاح")
            return True

        except Exception as e:
            music_logger.error(f"خطأ في تهيئة نواة الموسيقى: {e}")
            await error_handler.log_error(e, {"operation": "initialize_music_core"})
            return False
    
    async def join_voice_chat(self, chat_id: int) -> bool:
        """الانضمام إلى المكالمة الصوتية"""
        try:
            if chat_id in self.active_calls:
                return True
            
            # محاولة الانضمام
            await self.pytgcalls.join_group_call(
                chat_id,
                AudioPiped('https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'),
                stream_type=StreamType().local_stream
            )
            
            self.active_calls[chat_id] = True
            await set_playback_status(chat_id, "joined")
            
            print(f"تم الانضمام إلى المكالمة الصوتية: {chat_id}")
            return True
            
        except AlreadyJoinedError:
            self.active_calls[chat_id] = True
            return True
        except NoActiveGroupCall:
            print(f"لا توجد مكالمة صوتية نشطة في: {chat_id}")
            return False
        except Exception as e:
            print(f"خطأ في الانضمام إلى المكالمة الصوتية {chat_id}: {e}")
            return False
    
    async def leave_voice_chat(self, chat_id: int) -> bool:
        """مغادرة المكالمة الصوتية"""
        try:
            if chat_id not in self.active_calls:
                return True
            
            await self.pytgcalls.leave_group_call(chat_id)
            
            # تنظيف البيانات
            if chat_id in self.active_calls:
                del self.active_calls[chat_id]
            
            await clear_current_song(chat_id)
            await set_playback_status(chat_id, "stopped")
            
            print(f"تم مغادرة المكالمة الصوتية: {chat_id}")
            return True
            
        except NotInGroupCallError:
            if chat_id in self.active_calls:
                del self.active_calls[chat_id]
            return True
        except Exception as e:
            print(f"خطأ في مغادرة المكالمة الصوتية {chat_id}: {e}")
            return False
    
    @handle_music_errors
    @monitor_performance("play_audio")
    async def play_audio(self, chat_id: int, audio_path: str, song_info: Dict[str, Any]) -> bool:
        """تشغيل ملف صوتي"""
        try:
            # تسجيل الإجراء
            await log_music_action(
                "play_audio",
                chat_id,
                song_info.get('user_id', 0),
                {"title": song_info.get('title'), "audio_path": audio_path}
            )

            if not os.path.exists(audio_path):
                music_logger.error(f"الملف الصوتي غير موجود: {audio_path}")
                return False

            # التأكد من الانضمام إلى المكالمة
            if not await self.join_voice_chat(chat_id):
                return False

            # إنشاء stream الصوت
            audio_stream = AudioPiped(
                audio_path,
                HighQualityAudio()
            )

            # تشغيل الصوت
            await self.pytgcalls.change_stream(
                chat_id,
                audio_stream
            )

            # حفظ معلومات الأغنية
            await set_current_song(chat_id, song_info)
            await set_playback_status(chat_id, "playing")

            music_logger.info(f"تم تشغيل الأغنية: {song_info.get('title', 'غير معروف')} في المجموعة {chat_id}")
            return True

        except Exception as e:
            music_logger.error(f"خطأ في تشغيل الصوت {chat_id}: {e}")
            await error_handler.log_error(e, {
                "operation": "play_audio",
                "chat_id": chat_id,
                "audio_path": audio_path,
                "song_info": song_info
            })
            return False
    
    async def pause_audio(self, chat_id: int) -> bool:
        """إيقاف الصوت مؤقتاً"""
        try:
            if chat_id not in self.active_calls:
                return False
            
            await self.pytgcalls.pause_stream(chat_id)
            await set_playback_status(chat_id, "paused")
            
            print(f"تم إيقاف التشغيل مؤقتاً: {chat_id}")
            return True
            
        except Exception as e:
            print(f"خطأ في إيقاف التشغيل مؤقتاً {chat_id}: {e}")
            return False
    
    async def resume_audio(self, chat_id: int) -> bool:
        """استئناف التشغيل"""
        try:
            if chat_id not in self.active_calls:
                return False
            
            await self.pytgcalls.resume_stream(chat_id)
            await set_playback_status(chat_id, "playing")
            
            print(f"تم استئناف التشغيل: {chat_id}")
            return True
            
        except Exception as e:
            print(f"خطأ في استئناف التشغيل {chat_id}: {e}")
            return False
    
    async def stop_audio(self, chat_id: int) -> bool:
        """إيقاف التشغيل"""
        try:
            await self.leave_voice_chat(chat_id)
            return True
            
        except Exception as e:
            print(f"خطأ في إيقاف التشغيل {chat_id}: {e}")
            return False
    
    async def skip_audio(self, chat_id: int) -> bool:
        """تخطي الأغنية الحالية"""
        try:
            # الحصول على الأغنية التالية
            next_song = await get_next_song(chat_id)
            
            if next_song and next_song.get('audio_path'):
                # تشغيل الأغنية التالية
                return await self.play_audio(chat_id, next_song['audio_path'], next_song)
            else:
                # لا توجد أغاني في القائمة، إيقاف التشغيل
                return await self.stop_audio(chat_id)
            
        except Exception as e:
            print(f"خطأ في تخطي الأغنية {chat_id}: {e}")
            return False
    
    async def set_audio_volume(self, chat_id: int, volume: int) -> bool:
        """تعيين مستوى الصوت"""
        try:
            if chat_id not in self.active_calls:
                return False
            
            # التأكد من أن القيمة بين 0 و 100
            volume = max(0, min(100, volume))
            
            # تحويل إلى نسبة (0.0 - 1.0)
            volume_ratio = volume / 100.0
            
            # تطبيق مستوى الصوت (هذه الميزة قد تحتاج إلى تحديث pytgcalls)
            # await self.pytgcalls.set_volume(chat_id, volume_ratio)
            
            # حفظ الإعداد
            await set_volume(chat_id, volume)
            
            print(f"تم تعيين مستوى الصوت إلى {volume}% للمحادثة: {chat_id}")
            return True
            
        except Exception as e:
            print(f"خطأ في تعيين مستوى الصوت {chat_id}: {e}")
            return False
    
    async def get_call_status(self, chat_id: int) -> Dict[str, Any]:
        """الحصول على حالة المكالمة"""
        try:
            is_active = chat_id in self.active_calls
            current_song = await get_current_song(chat_id)
            playback_status = await get_playback_status(chat_id)
            volume = await get_volume(chat_id)
            
            return {
                'is_active': is_active,
                'current_song': current_song,
                'status': playback_status,
                'volume': volume
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على حالة المكالمة {chat_id}: {e}")
            return {
                'is_active': False,
                'current_song': None,
                'status': 'stopped',
                'volume': 50
            }
    
    # معالجات الأحداث
    async def _on_stream_end(self, client: PyTgCalls, update):
        """معالج انتهاء البث"""
        try:
            chat_id = update.chat_id
            print(f"انتهى البث في المحادثة: {chat_id}")
            
            # محاولة تشغيل الأغنية التالية
            await self.skip_audio(chat_id)
            
        except Exception as e:
            print(f"خطأ في معالج انتهاء البث: {e}")
    
    async def _on_kicked(self, client: PyTgCalls, update):
        """معالج الطرد من المكالمة"""
        try:
            chat_id = update.chat_id
            print(f"تم طرد البوت من المكالمة: {chat_id}")
            
            # تنظيف البيانات
            if chat_id in self.active_calls:
                del self.active_calls[chat_id]
            
            await clear_current_song(chat_id)
            await set_playback_status(chat_id, "stopped")
            
        except Exception as e:
            print(f"خطأ في معالج الطرد: {e}")
    
    async def _on_closed_voice_chat(self, client: PyTgCalls, update):
        """معالج إغلاق المكالمة الصوتية"""
        try:
            chat_id = update.chat_id
            print(f"تم إغلاق المكالمة الصوتية: {chat_id}")
            
            # تنظيف البيانات
            if chat_id in self.active_calls:
                del self.active_calls[chat_id]
            
            await clear_current_song(chat_id)
            await set_playback_status(chat_id, "stopped")
            
        except Exception as e:
            print(f"خطأ في معالج إغلاق المكالمة: {e}")


# إنشاء مثيل عام
music_core = MusicCore()
