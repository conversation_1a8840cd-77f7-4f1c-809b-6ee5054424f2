import asyncio
from datetime import datetime, timedelta
from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import (
    ban_user, unban_user, is_user_banned,
    ban_group, unban_group, is_group_banned,
    get_all_banned_users, get_all_banned_groups
)
from database.redis_db import redis_setex, redis_get, redis_delete, redis_keys
from utils.extraction import extract_group
from utils.logger import log_ban, log_unban


MUTED_USERS_PREFIX = "muted_user:"
MUTED_GROUPS_PREFIX = "muted_group:"


async def extract_user_enhanced(client: Client, message: Message):
    """استخراج معرف المستخدم مع دعم طرق متعددة"""
    user_id = None
    user_info = None

    
    if message.reply_to_message and message.reply_to_message.from_user:
        user_id = message.reply_to_message.from_user.id
        user_info = {
            'id': user_id,
            'first_name': message.reply_to_message.from_user.first_name,
            'username': message.reply_to_message.from_user.username
        }
        return user_id, user_info

    
    if len(message.command) > 1:
        text = message.command[1]

        
        if text.startswith("@"):
            username = text[1:]
            try:
                user = await client.get_users(username)
                user_id = user.id
                user_info = {
                    'id': user_id,
                    'first_name': user.first_name,
                    'username': user.username
                }
                return user_id, user_info
            except:
                return None, None

        
        if text.isdigit():
            user_id = int(text)
            try:
                user = await client.get_users(user_id)
                user_info = {
                    'id': user_id,
                    'first_name': user.first_name,
                    'username': user.username
                }
                return user_id, user_info
            except:
                
                return user_id, {'id': user_id, 'first_name': 'مستخدم غير معروف', 'username': None}

    return None, None


def parse_mute_duration(duration_str):
    """تحليل مدة الكتم من النص"""
    if not duration_str:
        return None

    duration_str = duration_str.lower()

    
    if duration_str.endswith('د') or duration_str.endswith('m'):
        try:
            minutes = int(duration_str[:-1])
            return timedelta(minutes=minutes)
        except:
            return None
    elif duration_str.endswith('س') or duration_str.endswith('h'):
        try:
            hours = int(duration_str[:-1])
            return timedelta(hours=hours)
        except:
            return None
    elif duration_str.endswith('ي') or duration_str.endswith('d'):
        try:
            days = int(duration_str[:-1])
            return timedelta(days=days)
        except:
            return None

    
    try:
        minutes = int(duration_str)
        return timedelta(minutes=minutes)
    except:
        return None


@Client.on_message(command_handler(["ban", "حظر"]) & dev)
@dev_only
async def ban_user_command(client: Client, message: Message):
    """معالجة أمر حظر مستخدم"""

    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد حظره.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`حظر @username`\n"
            "`حظر 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `حظر`"
        )
        return

    # التحقق من أن المستخدم ليس مطورًا
    from database.users_db import is_developer
    if await is_developer(user_id):
        await message.reply_text("⚠️ **لا يمكن حظر المطورين.**")
        return

    if await is_user_banned(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} محظور بالفعل.**"
        )
        return

    # حظر المستخدم
    success = await ban_user(user_id)
    if not success:
        await message.reply_text("❌ **فشل في حظر المستخدم.**")
        return

    # تسجيل الحظر
    reason = " ".join(message.command[2:]) if len(message.command) > 2 else None
    try:
        await log_ban(client, user_id, message.from_user.id, reason)
    except:
        pass  # تجاهل أخطاء التسجيل

    await client.ban_chat_member(message.chat.id, user_id)

    # إرسال رسالة التأكيد
    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    ban_text = f"✅ **تم حظر المستخدم {user_display} بنجاح.**\n"
    ban_text += f"🆔 **المعرف:** `{user_id}`"
    if reason:
        ban_text += f"\n📝 **السبب:** {reason}"

    await message.reply_text(ban_text)


@Client.on_message(command_handler(["unban", "الغاء حظر"]) & dev)
@dev_only
async def unban_user_command(client: Client, message: Message):
    """معالجة أمر إلغاء حظر مستخدم"""
    
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد إلغاء حظره.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`الغاء حظر @username`\n"
            "`الغاء حظر 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `الغاء حظر`"
        )
        return

    
    if not await is_user_banned(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} غير محظور.**"
        )
        return

    
    await unban_user(user_id)

    await client.unban_chat_member(message.chat.id, user_id)
    
    await log_unban(client, user_id, message.from_user.id)

    
    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"✅ **تم إلغاء حظر المستخدم {user_display} بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["mute", "كتم"]) & dev)
@dev_only
async def mute_user_command(client: Client, message: Message):
    """معالجة أمر كتم مستخدم"""
    
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد كتمه.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`كتم @username 30د`\n"
            "`كتم 123456789 2س`\n"
            "`كتم @username` (كتم دائم)\n"
            "أو الرد على رسالة المستخدم مع كتابة `كتم 1س`\n\n"
            "**وحدات الوقت:**\n"
            "• د = دقائق (مثال: 30د)\n"
            "• س = ساعات (مثال: 2س)\n"
            "• ي = أيام (مثال: 7ي)"
        )
        return

    
    duration = None
    duration_text = "دائم"

    if len(message.command) > 2:
        duration_str = message.command[2]
        duration = parse_mute_duration(duration_str)
        if duration:
            if duration.days > 0:
                duration_text = f"{duration.days} يوم"
            elif duration.seconds >= 3600:
                hours = duration.seconds // 3600
                duration_text = f"{hours} ساعة"
            else:
                minutes = duration.seconds // 60
                duration_text = f"{minutes} دقيقة"

    
    mute_key = f"{MUTED_USERS_PREFIX}{user_id}"
    if await redis_get(mute_key):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} مكتوم بالفعل.**"
        )
        return

    
    if duration:
        
        expire_time = int(duration.total_seconds())
        await redis_setex(mute_key, expire_time, "muted")
    else:
        
        await redis_setex(mute_key, 31536000, "muted")  

    
    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    mute_text = f"🔇 **تم كتم المستخدم {user_display} بنجاح.**\n"
    mute_text += f"🆔 **المعرف:** `{user_id}`\n"
    mute_text += f"⏰ **المدة:** {duration_text}"

    reason = " ".join(message.command[3:]) if len(message.command) > 3 else None
    if reason:
        mute_text += f"\n📝 **السبب:** {reason}"

    await message.reply_text(mute_text)


@Client.on_message(command_handler(["unmute", "الغاء كتم"]) & dev)
@dev_only
async def unmute_user_command(client: Client, message: Message):
    """معالجة أمر إلغاء كتم مستخدم"""
    
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد إلغاء كتمه.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`الغاء كتم @username`\n"
            "`الغاء كتم 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `الغاء كتم`"
        )
        return

    
    mute_key = f"{MUTED_USERS_PREFIX}{user_id}"
    if not await redis_get(mute_key):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} غير مكتوم.**"
        )
        return

    
    await redis_delete(mute_key)

    
    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"🔊 **تم إلغاء كتم المستخدم {user_display} بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["bangroup", "حظر مجموعة"]) & dev)
@dev_only
async def ban_group_command(client: Client, message: Message):
    """معالجة أمر حظر مجموعة"""
    
    group_id = await extract_group(message)

    if not group_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المجموعة المراد حظرها.**\n\n"
            "يمكنك تحديد المجموعة بإحدى الطرق التالية:\n"
            "- كتابة معرف المجموعة\n"
            "- كتابة اسم مستخدم المجموعة\n\n"
            "مثال: `حظر مجموعة @groupname`"
        )
        return

    
    if await is_group_banned(group_id):
        await message.reply_text(
            f"⚠️ **المجموعة {group_id} محظورة بالفعل.**"
        )
        return

    
    await ban_group(group_id)

    
    await message.reply_text(
        f"✅ **تم حظر المجموعة {group_id} بنجاح.**"
    )


@Client.on_message(command_handler(["unbangroup", "الغاء حظر مجموعة"]) & dev)
@dev_only
async def unban_group_command(client: Client, message: Message):
    """معالجة أمر إلغاء حظر مجموعة"""
    
    group_id = await extract_group(message)

    if not group_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المجموعة المراد إلغاء حظرها.**\n\n"
            "يمكنك تحديد المجموعة بإحدى الطرق التالية:\n"
            "- كتابة معرف المجموعة\n"
            "- كتابة اسم مستخدم المجموعة\n\n"
            "مثال: `الغاء حظر مجموعة @groupname`"
        )
        return

    
    if not await is_group_banned(group_id):
        await message.reply_text(
            f"⚠️ **المجموعة {group_id} غير محظورة.**"
        )
        return

    
    await unban_group(group_id)

    
    await message.reply_text(
        f"✅ **تم إلغاء حظر المجموعة {group_id} بنجاح.**"
    )


@Client.on_message(command_handler(["banlist", "قائمة المحظورين"]) & dev)
@dev_only
async def ban_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المستخدمين المحظورين"""
    
    banned_users = await get_all_banned_users()

    if not banned_users:
        await message.reply_text(
            "⚠️ **لا يوجد مستخدمين محظورين.**"
        )
        return

    
    ban_text = f"🚫 **قائمة المستخدمين المحظورين ({len(banned_users)}):**\n\n"

    for i, user_id in enumerate(banned_users, 1):
        ban_text += f"{i}. `{user_id}`\n"

    await message.reply_text(ban_text)


@Client.on_message(command_handler(["bangrouplist", "قائمة المجموعات المحظورة"]) & dev)
@dev_only
async def ban_group_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المجموعات المحظورة"""
    
    banned_groups = await get_all_banned_groups()

    if not banned_groups:
        await message.reply_text(
            "⚠️ **لا يوجد مجموعات محظورة.**"
        )
        return

    
    ban_text = f"🚫 **قائمة المجموعات المحظورة ({len(banned_groups)}):**\n\n"

    for i, group_id in enumerate(banned_groups, 1):
        ban_text += f"{i}. `{group_id}`\n"

    await message.reply_text(ban_text)


@Client.on_message(command_handler(["mutelist", "قائمة المكتومين"]) & dev)
@dev_only
async def mute_list_command(client: Client, message: Message):
    """معالجة أمر قائمة المستخدمين المكتومين"""
    
    muted_keys = await redis_keys(f"{MUTED_USERS_PREFIX}*")

    if not muted_keys:
        await message.reply_text(
            "⚠️ **لا يوجد مستخدمين مكتومين.**"
        )
        return

    
    muted_users = []
    for key in muted_keys:
        user_id = key.replace(MUTED_USERS_PREFIX, "")
        try:
            user_id = int(user_id)
            muted_users.append(user_id)
        except:
            continue

    
    mute_text = f"🔇 **قائمة المستخدمين المكتومين ({len(muted_users)}):**\n\n"

    for i, user_id in enumerate(muted_users, 1):
        try:
            
            user = await client.get_users(user_id)
            user_display = f"{user.first_name}"
            if user.username:
                user_display += f" (@{user.username})"
            mute_text += f"{i}. {user_display} - `{user_id}`\n"
        except:
            
            mute_text += f"{i}. مستخدم غير معروف - `{user_id}`\n"

    await message.reply_text(mute_text)
