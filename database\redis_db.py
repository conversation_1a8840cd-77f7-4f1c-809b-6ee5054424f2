import redis.asyncio as redis
import json
import logging
from config import REDIS_URI


LOGGER = logging.getLogger(__name__)


redis_connection = None

async def setup_redis():
    """إعداد اتصال Redis"""
    global redis_connection
    try:
        redis_connection = await redis.from_url(REDIS_URI)
        LOGGER.info("تم الاتصال بقاعدة بيانات Redis بنجاح")

        # اختبار الاتصال
        await redis_connection.ping()
        return True
    except Exception as e:
        LOGGER.error(f"فشل الاتصال بقاعدة بيانات Redis: {e}")
        return False


async def get_redis():
    """الحصول على اتصال Redis"""
    global redis_connection
    if redis_connection is None:
        await setup_redis()
    return redis_connection


async def redis_set(key, value):
    """تخزين قيمة في Redis"""
    if isinstance(value, (dict, list)):
        value = json.dumps(value)
    await redis_connection.set(key, value)

async def redis_get(key):
    """استرجاع قيمة من Redis"""
    value = await redis_connection.get(key)
    if value:
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value.decode("utf-8")
    return None

async def redis_delete(key):
    """حذف قيمة من Redis"""
    await redis_connection.delete(key)

async def redis_exists(key):
    """التحقق من وجود مفتاح في Redis"""
    return await redis_connection.exists(key)

async def redis_sadd(key, *values):
    """إضافة قيم إلى مجموعة في Redis"""
    await redis_connection.sadd(key, *values)

async def redis_srem(key, *values):
    """إزالة قيم من مجموعة في Redis"""
    await redis_connection.srem(key, *values)

async def redis_smembers(key):
    """استرجاع جميع قيم المجموعة من Redis"""
    members = await redis_connection.smembers(key)
    return [member.decode("utf-8") for member in members]

async def redis_sismember(key, value):
    """التحقق من وجود قيمة في مجموعة Redis"""
    return await redis_connection.sismember(key, value)

async def redis_hset(key, field, value):
    """تخزين قيمة في حقل داخل هاش Redis"""
    if isinstance(value, (dict, list)):
        value = json.dumps(value)
    await redis_connection.hset(key, field, value)

async def redis_hget(key, field):
    """استرجاع قيمة من حقل داخل هاش Redis"""
    value = await redis_connection.hget(key, field)
    if value:
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            return value.decode("utf-8")
    return None

async def redis_hdel(key, field):
    """حذف حقل من هاش Redis"""
    await redis_connection.hdel(key, field)

async def redis_hgetall(key):
    """استرجاع جميع الحقول والقيم من هاش Redis"""
    data = await redis_connection.hgetall(key)
    result = {}
    for k, v in data.items():
        k = k.decode("utf-8")
        try:
            result[k] = json.loads(v)
        except json.JSONDecodeError:
            result[k] = v.decode("utf-8")
    return result

async def redis_setex(key, seconds, value):
    """تخزين قيمة في Redis مع انتهاء صلاحية"""
    if isinstance(value, (dict, list)):
        value = json.dumps(value)
    await redis_connection.setex(key, seconds, value)

async def redis_keys(pattern):
    """البحث عن المفاتيح باستخدام نمط معين"""
    keys = await redis_connection.keys(pattern)
    return [key.decode("utf-8") for key in keys]

async def redis_expire(key, seconds):
    """تعيين انتهاء صلاحية لمفتاح موجود"""
    await redis_connection.expire(key, seconds)

async def redis_ttl(key):
    """الحصول على الوقت المتبقي لانتهاء صلاحية المفتاح"""
    return await redis_connection.ttl(key)
