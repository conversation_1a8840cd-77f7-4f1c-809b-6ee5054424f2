"""
وحدة قاعدة بيانات الموسيقى
تدير البيانات المتعلقة بالموسيقى والقوائم والإعدادات
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from database.redis_db import get_redis


class MusicDatabase:
    """فئة إدارة قاعدة بيانات الموسيقى"""
    
    def __init__(self):
        self.redis = None
    
    async def get_redis_client(self):
        """الحصول على عميل Redis"""
        if not self.redis:
            self.redis = await get_redis()
        return self.redis


# إدارة قوائم التشغيل
async def add_to_queue(chat_id: int, song_data: Dict[str, Any]) -> bool:
    """إضافة أغنية إلى قائمة الانتظار"""
    try:
        redis = await get_redis()
        queue_key = f"music_queue:{chat_id}"
        
        # إضافة الأغنية إلى نهاية القائمة
        await redis.rpush(queue_key, json.dumps(song_data, ensure_ascii=False))
        
        # تعيين انتهاء صلاحية القائمة بعد 24 ساعة
        await redis.expire(queue_key, 86400)
        
        return True
    except Exception as e:
        print(f"خطأ في إضافة الأغنية إلى القائمة: {e}")
        return False


async def get_queue(chat_id: int) -> List[Dict[str, Any]]:
    """الحصول على قائمة الانتظار"""
    try:
        redis = await get_redis()
        queue_key = f"music_queue:{chat_id}"
        
        # الحصول على جميع الأغاني في القائمة
        queue_data = await redis.lrange(queue_key, 0, -1)
        
        # تحويل البيانات من JSON
        queue = []
        for item in queue_data:
            try:
                song_data = json.loads(item)
                queue.append(song_data)
            except json.JSONDecodeError:
                continue
        
        return queue
    except Exception as e:
        print(f"خطأ في الحصول على القائمة: {e}")
        return []


async def get_next_song(chat_id: int) -> Optional[Dict[str, Any]]:
    """الحصول على الأغنية التالية وإزالتها من القائمة"""
    try:
        redis = await get_redis()
        queue_key = f"music_queue:{chat_id}"
        
        # الحصول على الأغنية الأولى وإزالتها
        song_data = await redis.lpop(queue_key)
        
        if song_data:
            return json.loads(song_data)
        return None
    except Exception as e:
        print(f"خطأ في الحصول على الأغنية التالية: {e}")
        return None


async def clear_queue(chat_id: int) -> bool:
    """مسح قائمة الانتظار"""
    try:
        redis = await get_redis()
        queue_key = f"music_queue:{chat_id}"
        
        await redis.delete(queue_key)
        return True
    except Exception as e:
        print(f"خطأ في مسح القائمة: {e}")
        return False


async def get_queue_length(chat_id: int) -> int:
    """الحصول على طول قائمة الانتظار"""
    try:
        redis = await get_redis()
        queue_key = f"music_queue:{chat_id}"
        
        return await redis.llen(queue_key)
    except Exception as e:
        print(f"خطأ في الحصول على طول القائمة: {e}")
        return 0


# إدارة الأغنية الحالية
async def set_current_song(chat_id: int, song_data: Dict[str, Any]) -> bool:
    """تعيين الأغنية الحالية"""
    try:
        redis = await get_redis()
        current_key = f"current_song:{chat_id}"
        
        await redis.set(current_key, json.dumps(song_data, ensure_ascii=False), ex=86400)
        return True
    except Exception as e:
        print(f"خطأ في تعيين الأغنية الحالية: {e}")
        return False


async def get_current_song(chat_id: int) -> Optional[Dict[str, Any]]:
    """الحصول على الأغنية الحالية"""
    try:
        redis = await get_redis()
        current_key = f"current_song:{chat_id}"
        
        song_data = await redis.get(current_key)
        if song_data:
            return json.loads(song_data)
        return None
    except Exception as e:
        print(f"خطأ في الحصول على الأغنية الحالية: {e}")
        return None


async def clear_current_song(chat_id: int) -> bool:
    """مسح الأغنية الحالية"""
    try:
        redis = await get_redis()
        current_key = f"current_song:{chat_id}"
        
        await redis.delete(current_key)
        return True
    except Exception as e:
        print(f"خطأ في مسح الأغنية الحالية: {e}")
        return False


# إدارة حالة التشغيل
async def set_playback_status(chat_id: int, status: str) -> bool:
    """تعيين حالة التشغيل (playing, paused, stopped)"""
    try:
        redis = await get_redis()
        status_key = f"playback_status:{chat_id}"
        
        await redis.set(status_key, status, ex=86400)
        return True
    except Exception as e:
        print(f"خطأ في تعيين حالة التشغيل: {e}")
        return False


async def get_playback_status(chat_id: int) -> str:
    """الحصول على حالة التشغيل"""
    try:
        redis = await get_redis()
        status_key = f"playback_status:{chat_id}"
        
        status = await redis.get(status_key)
        return status.decode() if status else "stopped"
    except Exception as e:
        print(f"خطأ في الحصول على حالة التشغيل: {e}")
        return "stopped"


# إدارة إعدادات الموسيقى
async def set_music_setting(chat_id: int, setting: str, value: Any) -> bool:
    """تعيين إعداد موسيقى"""
    try:
        redis = await get_redis()
        setting_key = f"music_settings:{chat_id}:{setting}"
        
        await redis.set(setting_key, json.dumps(value), ex=86400)
        return True
    except Exception as e:
        print(f"خطأ في تعيين الإعداد: {e}")
        return False


async def get_music_setting(chat_id: int, setting: str, default: Any = None) -> Any:
    """الحصول على إعداد موسيقى"""
    try:
        redis = await get_redis()
        setting_key = f"music_settings:{chat_id}:{setting}"
        
        value = await redis.get(setting_key)
        if value:
            return json.loads(value)
        return default
    except Exception as e:
        print(f"خطأ في الحصول على الإعداد: {e}")
        return default


# إدارة مستوى الصوت
async def set_volume(chat_id: int, volume: int) -> bool:
    """تعيين مستوى الصوت (0-100)"""
    volume = max(0, min(100, volume))  # التأكد من أن القيمة بين 0 و 100
    return await set_music_setting(chat_id, "volume", volume)


async def get_volume(chat_id: int) -> int:
    """الحصول على مستوى الصوت"""
    return await get_music_setting(chat_id, "volume", 50)


# إدارة وضع التكرار
async def set_repeat_mode(chat_id: int, mode: str) -> bool:
    """تعيين وضع التكرار (off, single, all)"""
    if mode not in ["off", "single", "all"]:
        mode = "off"
    return await set_music_setting(chat_id, "repeat_mode", mode)


async def get_repeat_mode(chat_id: int) -> str:
    """الحصول على وضع التكرار"""
    return await get_music_setting(chat_id, "repeat_mode", "off")


# إدارة وضع الخلط
async def set_shuffle_mode(chat_id: int, enabled: bool) -> bool:
    """تعيين وضع الخلط"""
    return await set_music_setting(chat_id, "shuffle_mode", enabled)


async def get_shuffle_mode(chat_id: int) -> bool:
    """الحصول على وضع الخلط"""
    return await get_music_setting(chat_id, "shuffle_mode", False)


# إحصائيات الاستخدام
async def increment_play_count(chat_id: int) -> int:
    """زيادة عداد التشغيل"""
    try:
        redis = await get_redis()
        count_key = f"play_count:{chat_id}"
        
        count = await redis.incr(count_key)
        await redis.expire(count_key, 86400 * 30)  # انتهاء الصلاحية بعد 30 يوم
        
        return count
    except Exception as e:
        print(f"خطأ في زيادة عداد التشغيل: {e}")
        return 0


async def get_play_count(chat_id: int) -> int:
    """الحصول على عداد التشغيل"""
    try:
        redis = await get_redis()
        count_key = f"play_count:{chat_id}"
        
        count = await redis.get(count_key)
        return int(count) if count else 0
    except Exception as e:
        print(f"خطأ في الحصول على عداد التشغيل: {e}")
        return 0
