from pyrogram import Client, filters
from pyrogram.types import Message
from database.users_db import is_user_banned
from database.redis_db import redis_get

MUTED_USERS_PREFIX = "muted_user:"


async def check_user_banned(client: Client, message: Message):
    """فحص ما إذا كان المستخدم محظورًا"""
    if not message.from_user:
        return False
    
    user_id = message.from_user.id
    return await is_user_banned(user_id)


async def check_user_muted(client: Client, message: Message):
    """فحص ما إذا كان المستخدم مكتومًا"""
    if not message.from_user:
        return False
    
    user_id = message.from_user.id
    mute_key = f"{MUTED_USERS_PREFIX}{user_id}"
    return await redis_get(mute_key) is not None


async def ban_mute_middleware(client: Client, message: Message):
    """Middleware لفحص الحظر والكتم"""
    # تجاهل الرسائل من المطورين
    from database.users_db import is_developer
    if message.from_user and await is_developer(message.from_user.id):
        return True

    # فحص الحظر
    if await check_user_banned(client, message):
        return False

    # فحص الكتم
    if await check_user_muted(client, message):
        # حذف الرسالة إذا كان المستخدم مكتومًا
        try:
            await message.delete()
        except:
            pass
        return False

    return True


# فلتر للتحقق من الحظر والكتم
def not_banned_or_muted():
    """فلتر للتحقق من أن المستخدم غير محظور أو مكتوم"""
    async def func(flt, client, message):
        return await ban_mute_middleware(client, message)
    
    return filters.create(func)
