"""
وحدة التحكم الإداري للموسيقى
تدير الأوامر الإدارية الخاصة بنظام الموسيقى
"""

from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from helpers.decorators import check_banned, check_force_sub, group_only
from helpers.filters import admin, dev_filter
from helpers.command_handler import command_handler
from database.music_db import (
    clear_all_data, get_usage_stats, reset_usage_stats,
    get_all_active_chats, clear_chat_data
)
from database.settings_db import (
    is_youtube_enabled, set_youtube_enabled,
    is_audio_enabled, set_audio_enabled
)
from plugins.music.core import music_core


@Client.on_message(command_handler(["music admin", "إدارة الموسيقى"]))
@check_banned
@check_force_sub
@group_only
async def music_admin_command(client: Client, message: Message):
    """لوحة التحكم الإدارية للموسيقى"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        # الحصول على الإعدادات الحالية
        youtube_enabled = await is_youtube_enabled()
        audio_enabled = await is_audio_enabled()
        
        # الحصول على الإحصائيات
        stats = await get_usage_stats()
        active_chats = await get_all_active_chats()
        
        text = "🎵 **لوحة التحكم الإدارية للموسيقى**\n\n"
        text += "⚙️ **الإعدادات الحالية:**\n"
        text += f"📺 اليوتيوب: {'مفعل ✅' if youtube_enabled else 'معطل ❌'}\n"
        text += f"🎵 الصوت: {'مفعل ✅' if audio_enabled else 'معطل ❌'}\n\n"
        
        text += "📊 **الإحصائيات:**\n"
        text += f"🎧 إجمالي التشغيلات: {stats.get('total_plays', 0)}\n"
        text += f"💬 المجموعات النشطة: {len(active_chats)}\n"
        text += f"📅 آخر تشغيل: {stats.get('last_play_time', 'غير محدد')}\n\n"
        
        text += "استخدم الأزرار أدناه للتحكم:"

        buttons = [
            [
                InlineKeyboardButton(
                    f"📺 {'تعطيل' if youtube_enabled else 'تفعيل'} اليوتيوب",
                    callback_data=f"toggle_youtube_{not youtube_enabled}"
                ),
                InlineKeyboardButton(
                    f"🎵 {'تعطيل' if audio_enabled else 'تفعيل'} الصوت",
                    callback_data=f"toggle_audio_{not audio_enabled}"
                )
            ],
            [
                InlineKeyboardButton("📊 الإحصائيات التفصيلية", callback_data="detailed_stats"),
                InlineKeyboardButton("🗑 مسح البيانات", callback_data="clear_data_menu")
            ],
            [
                InlineKeyboardButton("🔄 إعادة تشغيل النظام", callback_data="restart_music_system"),
                InlineKeyboardButton("⚙️ إعدادات متقدمة", callback_data="advanced_settings")
            ]
        ]

        await message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(buttons)
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في عرض لوحة التحكم:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["force stop all", "إيقاف إجباري شامل"]))
@check_banned
@check_force_sub
async def force_stop_all_command(client: Client, message: Message):
    """إيقاف إجباري لجميع التشغيلات"""
    try:
        if not await dev_filter.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمطورين.**"
            )
            return

        # إيقاف جميع التشغيلات
        active_chats = await get_all_active_chats()
        stopped_count = 0
        
        for chat_id in active_chats:
            try:
                success = await music_core.force_stop_audio(chat_id)
                if success:
                    stopped_count += 1
            except Exception as e:
                print(f"خطأ في إيقاف التشغيل في المجموعة {chat_id}: {e}")

        await message.reply_text(
            f"⏹ **تم إيقاف التشغيل في {stopped_count} مجموعة من أصل {len(active_chats)}.**"
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في الإيقاف الإجباري:**\n`{str(e)}`"
        )


@Client.on_message(command_handler(["music stats", "إحصائيات الموسيقى"]))
@check_banned
@check_force_sub
async def music_stats_command(client: Client, message: Message):
    """عرض إحصائيات الموسيقى التفصيلية"""
    try:
        if not await admin.func(client, message):
            await message.reply_text(
                "⚠️ **هذا الأمر متاح فقط للمشرفين.**"
            )
            return

        # الحصول على الإحصائيات
        stats = await get_usage_stats()
        active_chats = await get_all_active_chats()
        
        text = "📊 **إحصائيات الموسيقى التفصيلية**\n\n"
        
        text += "🎧 **إحصائيات التشغيل:**\n"
        text += f"• إجمالي التشغيلات: {stats.get('total_plays', 0)}\n"
        text += f"• التشغيلات اليوم: {stats.get('today_plays', 0)}\n"
        text += f"• التشغيلات هذا الأسبوع: {stats.get('week_plays', 0)}\n"
        text += f"• التشغيلات هذا الشهر: {stats.get('month_plays', 0)}\n\n"
        
        text += "💬 **إحصائيات المجموعات:**\n"
        text += f"• المجموعات النشطة: {len(active_chats)}\n"
        text += f"• إجمالي المجموعات المسجلة: {stats.get('total_chats', 0)}\n\n"
        
        text += "⏰ **معلومات زمنية:**\n"
        text += f"• آخر تشغيل: {stats.get('last_play_time', 'غير محدد')}\n"
        text += f"• وقت التشغيل الإجمالي: {stats.get('total_duration', '0 دقيقة')}\n"
        text += f"• متوسط مدة الأغنية: {stats.get('avg_duration', '0 دقيقة')}\n\n"
        
        text += "🎵 **الأغاني الأكثر تشغيلاً:**\n"
        top_songs = stats.get('top_songs', [])
        if top_songs:
            for i, song in enumerate(top_songs[:5], 1):
                text += f"{i}. {song.get('title', 'غير معروف')} ({song.get('plays', 0)} مرة)\n"
        else:
            text += "لا توجد بيانات متاحة.\n"

        buttons = [
            [
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh_stats"),
                InlineKeyboardButton("📊 تصدير البيانات", callback_data="export_stats")
            ],
            [
                InlineKeyboardButton("🗑 إعادة تعيين الإحصائيات", callback_data="reset_stats_confirm")
            ]
        ]

        await message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(buttons)
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **خطأ في عرض الإحصائيات:**\n`{str(e)}`"
        )


# معالجات الأزرار
@Client.on_callback_query(filters.regex("^(toggle_youtube_|toggle_audio_|detailed_stats|clear_data_menu|restart_music_system|advanced_settings)"))
async def music_admin_callback(client, callback_query):
    """معالجة أزرار لوحة التحكم الإدارية"""
    try:
        data = callback_query.data
        message = callback_query.message
        user_id = callback_query.from_user.id

        # التحقق من الصلاحيات
        if message.chat.type != "private":
            member = await client.get_chat_member(message.chat.id, user_id)
            if member.status not in ["creator", "administrator"]:
                await callback_query.answer(
                    "⚠️ هذا الأمر متاح فقط للمشرفين.",
                    show_alert=True
                )
                return

        if data.startswith("toggle_youtube_"):
            # تغيير حالة اليوتيوب
            new_state = data.split("_")[-1] == "True"
            await set_youtube_enabled(new_state)
            status = "تم تفعيل" if new_state else "تم تعطيل"
            await callback_query.answer(f"📺 {status} اليوتيوب.")
            
        elif data.startswith("toggle_audio_"):
            # تغيير حالة الصوت
            new_state = data.split("_")[-1] == "True"
            await set_audio_enabled(new_state)
            status = "تم تفعيل" if new_state else "تم تعطيل"
            await callback_query.answer(f"🎵 {status} الصوت.")
            
        elif data == "detailed_stats":
            # عرض الإحصائيات التفصيلية
            await callback_query.answer("📊 جاري تحميل الإحصائيات...")
            # استدعاء دالة الإحصائيات
            from plugins.music.admin import music_stats_command
            await music_stats_command(client, message)
            
        elif data == "restart_music_system":
            # إعادة تشغيل نظام الموسيقى
            await callback_query.answer("🔄 جاري إعادة تشغيل النظام...")
            try:
                await music_core.restart()
                await callback_query.answer("✅ تم إعادة تشغيل النظام بنجاح.")
            except Exception as e:
                await callback_query.answer(f"❌ فشل في إعادة التشغيل: {str(e)}", show_alert=True)

        # تحديث الرسالة
        await update_admin_panel(client, message)

    except Exception as e:
        await callback_query.answer(f"❌ خطأ: {str(e)}", show_alert=True)


async def update_admin_panel(client: Client, message: Message):
    """تحديث لوحة التحكم الإدارية"""
    try:
        # الحصول على الإعدادات الحالية
        youtube_enabled = await is_youtube_enabled()
        audio_enabled = await is_audio_enabled()
        
        # الحصول على الإحصائيات
        stats = await get_usage_stats()
        active_chats = await get_all_active_chats()
        
        text = "🎵 **لوحة التحكم الإدارية للموسيقى**\n\n"
        text += "⚙️ **الإعدادات الحالية:**\n"
        text += f"📺 اليوتيوب: {'مفعل ✅' if youtube_enabled else 'معطل ❌'}\n"
        text += f"🎵 الصوت: {'مفعل ✅' if audio_enabled else 'معطل ❌'}\n\n"
        
        text += "📊 **الإحصائيات:**\n"
        text += f"🎧 إجمالي التشغيلات: {stats.get('total_plays', 0)}\n"
        text += f"💬 المجموعات النشطة: {len(active_chats)}\n"
        text += f"📅 آخر تشغيل: {stats.get('last_play_time', 'غير محدد')}\n\n"
        
        text += "استخدم الأزرار أدناه للتحكم:"

        buttons = [
            [
                InlineKeyboardButton(
                    f"📺 {'تعطيل' if youtube_enabled else 'تفعيل'} اليوتيوب",
                    callback_data=f"toggle_youtube_{not youtube_enabled}"
                ),
                InlineKeyboardButton(
                    f"🎵 {'تعطيل' if audio_enabled else 'تفعيل'} الصوت",
                    callback_data=f"toggle_audio_{not audio_enabled}"
                )
            ],
            [
                InlineKeyboardButton("📊 الإحصائيات التفصيلية", callback_data="detailed_stats"),
                InlineKeyboardButton("🗑 مسح البيانات", callback_data="clear_data_menu")
            ],
            [
                InlineKeyboardButton("🔄 إعادة تشغيل النظام", callback_data="restart_music_system"),
                InlineKeyboardButton("⚙️ إعدادات متقدمة", callback_data="advanced_settings")
            ]
        ]

        await message.edit_text(
            text,
            reply_markup=InlineKeyboardMarkup(buttons)
        )

    except Exception as e:
        print(f"خطأ في تحديث لوحة التحكم: {e}")
