from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from database.settings_db import get_channel, get_group, get_bot_name, get_source_name


_bot_name = None
_source_name = None
_channel = None
_group = None


async def update_globals():
    """تحديث المتغيرات العامة"""
    global _bot_name, _source_name, _channel, _group
    _bot_name = await get_bot_name()
    _source_name = await get_source_name()
    _channel = await get_channel()
    _group = await get_group()


def setup_keyboards():
    """إعداد لوحات المفاتيح"""
    pass  


async def start_keyboard():
    """لوحة مفاتيح البداية"""
    await update_globals()
    buttons = [
        [
            InlineKeyboardButton("➕ أضفني إلى مجموعتك", url=f"https://t.me/Hqq5Bot?startgroup=true")
        ],
        [
            InlineKeyboardButton("📚 الأوامر", callback_data="commands"),
            InlineKeyboardButton("ℹ️ حول", callback_data="about")
        ],
        [
            InlineKeyboardButton("📢 القناة", url=_channel),
            InlineKeyboardButton("👥 المجموعة", url=_group)
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def help_keyboard():
    """لوحة مفاتيح المساعدة"""
    buttons = [
        [
            InlineKeyboardButton("🎵 أوامر التشغيل", callback_data="music_commands")
        ],
        [
            InlineKeyboardButton("⚙️ أوامر الإدارة", callback_data="admin_commands"),
            InlineKeyboardButton("🔰 أوامر عامة", callback_data="general_commands")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def about_keyboard():
    """لوحة مفاتيح حول البوت"""
    await update_globals()
    buttons = [
        [
            InlineKeyboardButton("📢 القناة", url=_channel),
            InlineKeyboardButton("👥 المجموعة", url=_group)
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def player_keyboard(video_url=None):
    """لوحة مفاتيح التشغيل"""
    buttons = [
        [
            InlineKeyboardButton("⏸ إيقاف مؤقت", callback_data="pause"),
            InlineKeyboardButton("▶️ استئناف", callback_data="resume"),
            InlineKeyboardButton("⏭ تخطي", callback_data="skip")
        ],
        [
            InlineKeyboardButton("🔄 تكرار", callback_data="repeat"),
            InlineKeyboardButton("🔀 خلط", callback_data="shuffle"),
            InlineKeyboardButton("⏹ إيقاف", callback_data="stop")
        ]
    ]
    
    if video_url:
        buttons.append([
            InlineKeyboardButton("🔗 رابط الفيديو", url=video_url)
        ])
    
    buttons.append([
        InlineKeyboardButton("🎵 قائمة التشغيل", callback_data="playlist")
    ])
    
    return InlineKeyboardMarkup(buttons)


async def playlist_keyboard(page=1, total_pages=1):
    """لوحة مفاتيح قائمة التشغيل"""
    buttons = []
    
    
    nav_buttons = []
    if page > 1:
        nav_buttons.append(InlineKeyboardButton("⬅️", callback_data=f"playlist_page_{page-1}"))
    
    nav_buttons.append(InlineKeyboardButton(f"{page}/{total_pages}", callback_data="noop"))
    
    if page < total_pages:
        nav_buttons.append(InlineKeyboardButton("➡️", callback_data=f"playlist_page_{page+1}"))
    
    if nav_buttons:
        buttons.append(nav_buttons)
    
    
    buttons.append([
        InlineKeyboardButton("🔙 رجوع", callback_data="back_to_player")
    ])
    
    return InlineKeyboardMarkup(buttons)


async def dev_keyboard():
    """لوحة مفاتيح المطور"""
    buttons = [
        [
            InlineKeyboardButton("📊 الإحصائيات", callback_data="stats"),
            InlineKeyboardButton("📢 الإذاعات", callback_data="broadcast")
        ],
        [
            InlineKeyboardButton("⚙️ إعدادات البوت", callback_data="bot_settings"),
            InlineKeyboardButton("👤 إدارة المساعد", callback_data="assistant_settings")
        ],
        [
            InlineKeyboardButton("📝 سجل التشغيل", callback_data="log_settings"),
            InlineKeyboardButton("🚫 الحظر", callback_data="ban_settings")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_start")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def bot_settings_keyboard():
    """لوحة مفاتيح إعدادات البوت"""
    buttons = [
        [
            InlineKeyboardButton("✏️ تعيين اسم البوت", callback_data="set_bot_name"),
            InlineKeyboardButton("✏️ تعيين اسم السورس", callback_data="set_source_name")
        ],
        [
            InlineKeyboardButton("✏️ تعيين اسم المطور", callback_data="set_developer_name"),
            InlineKeyboardButton("✏️ تعيين قناة البوت", callback_data="set_channel")
        ],
        [
            InlineKeyboardButton("✏️ تعيين مجموعة البوت", callback_data="set_group"),
            InlineKeyboardButton("🔄 الاشتراك الإجباري", callback_data="toggle_force_sub")
        ],
        [
            InlineKeyboardButton("🔄 صلاحيات التشغيل المتقدمة", callback_data="toggle_advanced_perms"),
            InlineKeyboardButton("🔄 التواصل", callback_data="toggle_communication")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def broadcast_keyboard():
    """لوحة مفاتيح الإذاعات"""
    buttons = [
        [
            InlineKeyboardButton("📢 إذاعة عامة", callback_data="broadcast_all"),
            InlineKeyboardButton("📢 إذاعة للمستخدمين", callback_data="broadcast_users")
        ],
        [
            InlineKeyboardButton("📢 إذاعة للمجموعات", callback_data="broadcast_groups"),
            InlineKeyboardButton("🔊 إذاعة صوتية", callback_data="broadcast_voice")
        ],
        [
            InlineKeyboardButton("↪️ توجيه عام", callback_data="forward_all"),
            InlineKeyboardButton("↪️ توجيه للمستخدمين", callback_data="forward_users")
        ],
        [
            InlineKeyboardButton("↪️ توجيه للمجموعات", callback_data="forward_groups"),
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def assistant_keyboard():
    """لوحة مفاتيح إعدادات المساعد"""
    from database.settings_db import get_all_assistant_accounts

    
    assistants = await get_all_assistant_accounts()
    has_assistant = bool(assistants)

    buttons = []

    if has_assistant:
        
        buttons.extend([
            [
                InlineKeyboardButton("👤 عرض معلومات المساعد", callback_data="assistant_info"),
                InlineKeyboardButton("🔄 تحديث معلومات المساعد", callback_data="assistant_refresh")
            ],
            [
                InlineKeyboardButton("✏️ تعديل الاسم الأول", callback_data="assistant_edit_first_name"),
                InlineKeyboardButton("✏️ تعديل الاسم الثاني", callback_data="assistant_edit_last_name")
            ],
            [
                InlineKeyboardButton("✏️ تعديل البايو", callback_data="assistant_edit_bio"),
                InlineKeyboardButton("✏️ تعديل اسم المستخدم", callback_data="assistant_edit_username")
            ],
            [
                InlineKeyboardButton("🖼 إضافة صورة شخصية", callback_data="assistant_add_photo"),
                InlineKeyboardButton("🗑 حذف الصورة الشخصية", callback_data="assistant_remove_photo")
            ],
            [
                InlineKeyboardButton("👋 دعوة المساعد للمجموعة", callback_data="assistant_invite"),
                InlineKeyboardButton("🚪 خروج المساعد من المجموعات", callback_data="assistant_leave_groups")
            ],
            [
                InlineKeyboardButton("🗑 حذف الحساب المساعد", callback_data="assistant_remove_confirm")
            ]
        ])
    else:
        
        buttons.append([
            InlineKeyboardButton("➕ إضافة حساب مساعد جديد", callback_data="assistant_add_new")
        ])

    
    buttons.append([
        InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
    ])

    return InlineKeyboardMarkup(buttons)


async def assistant_remove_confirm_keyboard():
    """لوحة مفاتيح تأكيد حذف المساعد"""
    buttons = [
        [
            InlineKeyboardButton("✅ نعم، احذف الحساب", callback_data="assistant_remove_confirmed"),
            InlineKeyboardButton("❌ إلغاء", callback_data="assistant_settings")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def assistant_photo_keyboard():
    """لوحة مفاتيح إعدادات صورة المساعد"""
    buttons = [
        [
            InlineKeyboardButton("📷 رفع صورة جديدة", callback_data="assistant_upload_photo"),
            InlineKeyboardButton("🔗 رابط صورة", callback_data="assistant_photo_url")
        ],
        [
            InlineKeyboardButton("🗑 حذف الصورة الحالية", callback_data="assistant_delete_photo"),
            InlineKeyboardButton("🔙 رجوع", callback_data="assistant_settings")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def assistant_groups_keyboard():
    """لوحة مفاتيح إعدادات المجموعات للمساعد"""
    buttons = [
        [
            InlineKeyboardButton("👋 دعوة للمجموعة الحالية", callback_data="assistant_invite_current"),
            InlineKeyboardButton("🔗 دعوة برابط", callback_data="assistant_invite_link")
        ],
        [
            InlineKeyboardButton("🚪 خروج من المجموعة الحالية", callback_data="assistant_leave_current"),
            InlineKeyboardButton("🚪 خروج من جميع المجموعات", callback_data="assistant_leave_all")
        ],
        [
            InlineKeyboardButton("📋 عرض المجموعات المنضم إليها", callback_data="assistant_list_groups"),
            InlineKeyboardButton("🔙 رجوع", callback_data="assistant_settings")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def cancel_keyboard():
    """لوحة مفاتيح إلغاء العملية"""
    buttons = [
        [
            InlineKeyboardButton("❌ إلغاء العملية", callback_data="assistant_settings")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def log_settings_keyboard():
    """لوحة مفاتيح إعدادات سجل التشغيل"""
    buttons = [
        [
            InlineKeyboardButton("🔄 تفعيل/تعطيل سجل التشغيل", callback_data="toggle_log"),
            InlineKeyboardButton("✏️ تغيير مكان سجل التشغيل", callback_data="set_log_channel")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def ban_settings_keyboard():
    """لوحة مفاتيح إعدادات الحظر"""
    buttons = [
        [
            InlineKeyboardButton("🚫 حظر مستخدم", callback_data="ban_user"),
            InlineKeyboardButton("✅ إلغاء حظر مستخدم", callback_data="unban_user")
        ],
        [
            InlineKeyboardButton("🚫 حظر مجموعة", callback_data="ban_group"),
            InlineKeyboardButton("✅ إلغاء حظر مجموعة", callback_data="unban_group")
        ],
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
        ]
    ]
    return InlineKeyboardMarkup(buttons)


async def promotion_keyboard():
    """لوحة مفاتيح الترويج"""
    buttons = [
        [
            InlineKeyboardButton("📢 ترويج عام", callback_data="promo_general"),
            InlineKeyboardButton("🕌 ترويج لأذان", callback_data="promo_azan")
        ],
        [
            InlineKeyboardButton("🛡 ترويج لحماية", callback_data="promo_protection"),
            InlineKeyboardButton("🔄 ترويج لتحديث", callback_data="promo_update")
        ],
        [
            InlineKeyboardButton("❌ إلغاء الترويج", callback_data="cancel_promo"),
            InlineKeyboardButton("🔙 رجوع", callback_data="back_to_dev")
        ]
    ]
    return InlineKeyboardMarkup(buttons)
