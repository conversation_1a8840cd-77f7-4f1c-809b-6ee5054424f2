import re
from pyrogram.types import Message


async def extract_user(message: Message):
    """استخراج معرف المستخدم من الرسالة"""
    user_id = None
    
    
    if message.reply_to_message and message.reply_to_message.from_user:
        return message.reply_to_message.from_user.id
    
    
    if len(message.command) > 1:
        text = message.command[1]
        
        
        if text.startswith("@"):
            username = text[1:]
            return username  
        
        
        if text.isdigit():
            return int(text)
    
    
    if message.entities:
        for entity in message.entities:
            if entity.type == "mention":
                return message.text[entity.offset:entity.offset + entity.length][1:]
            
            if entity.type == "text_mention":
                return entity.user.id
    
    
    return message.from_user.id


async def extract_group(message: Message):
    """استخراج معرف المجموعة من الرسالة"""
    
    if len(message.command) > 1:
        text = message.command[1]
        
        
        if text.startswith("@"):
            return text[1:]  
        
        
        if text.isdigit() or text.startswith("-100"):
            return int(text.replace("-100", ""))
        
        
        if "t.me/" in text:
            match = re.search(r"t\.me/(?:c/)?([^/]+)", text)
            if match:
                return match.group(1)
    
    
    return message.chat.id if message.chat.type in ["group", "supergroup"] else None


async def extract_link(message: Message):
    """استخراج رابط من الرسالة"""
    
    if len(message.command) > 1:
        text = " ".join(message.command[1:])
        
        
        url_pattern = re.compile(r'https?://\S+')
        match = url_pattern.search(text)
        if match:
            return match.group(0)
    
    
    if message.reply_to_message and message.reply_to_message.text:
        text = message.reply_to_message.text
        
        
        url_pattern = re.compile(r'https?://\S+')
        match = url_pattern.search(text)
        if match:
            return match.group(0)
    
    
    return None


async def extract_text(message: Message):
    """استخراج نص من الرسالة"""
    
    if len(message.command) > 1:
        return " ".join(message.command[1:])
    
    
    if message.reply_to_message:
        if message.reply_to_message.text:
            return message.reply_to_message.text
        elif message.reply_to_message.caption:
            return message.reply_to_message.caption
    
    
    return ""


async def extract_song_name(message: Message):
    """استخراج اسم الأغنية من الرسالة"""
    
    if len(message.command) > 1:
        return " ".join(message.command[1:])
    
    
    if message.reply_to_message:
        if message.reply_to_message.text:
            return message.reply_to_message.text
        elif message.reply_to_message.caption:
            return message.reply_to_message.caption
    
    
    return None
