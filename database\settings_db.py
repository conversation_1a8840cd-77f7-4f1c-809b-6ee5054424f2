import logging
import time
from database.redis_db import (
    redis_set, redis_get, redis_delete, redis_exists,
    redis_hset, redis_hget, redis_hdel, redis_hgetall
)
from config import (
    DEFAULT_BOT_NAME, DEFAULT_SOURCE_NAME,
    DEFAULT_CHANNEL, DEFAULT_GROUP
)


LOGGER = logging.getLogger(__name__)


BOT_INFO_KEY = "bot_info"  


async def get_bot_id():
    """الحصول على معرف البوت من Redis"""
    bot_id = await redis_get(f"{BOT_INFO_KEY}:id")
    return bot_id


async def set_bot_id(bot_id):
    """تعيين معرف البوت في Redis"""
    bot_id_str = str(bot_id)
    await redis_set(f"{BOT_INFO_KEY}:id", bot_id_str)
    LOGGER.info(f"تم تعيين معرف البوت: {bot_id_str}")
    return True


async def get_prefix(prefix_type):
    """الحصول على البادئة المستخدمة في Redis"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم العثور على معرف البوت")
        return None

    if prefix_type == "settings":
        return f"settings:{bot_id}:"
    elif prefix_type == "force_sub":
        return f"force_sub:{bot_id}"
    elif prefix_type == "advanced_perms":
        return f"advanced_perms:{bot_id}"
    elif prefix_type == "promotion":
        return f"promotion:{bot_id}"
    elif prefix_type == "log_channel":
        return f"log_channel:{bot_id}"
    elif prefix_type == "assistant_accounts":
        return f"assistant_accounts:{bot_id}"
    else:
        return None


async def get_bot_name():
    """الحصول على اسم البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        return DEFAULT_BOT_NAME

    settings_prefix = await get_prefix("settings")
    name = await redis_hget(f"{settings_prefix}bot_info", "bot_name")
    return name if name else DEFAULT_BOT_NAME

async def set_bot_name(name):
    """تعيين اسم البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}bot_info", "bot_name", name)
    LOGGER.info(f"تم تعيين اسم البوت: {name}")
    return True

async def get_source_name():
    """الحصول على اسم السورس"""
    bot_id = await get_bot_id()
    if not bot_id:
        return DEFAULT_SOURCE_NAME

    settings_prefix = await get_prefix("settings")
    name = await redis_hget(f"{settings_prefix}bot_info", "source_name")
    return name if name else DEFAULT_SOURCE_NAME

async def set_source_name(name):
    """تعيين اسم السورس"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}bot_info", "source_name", name)
    LOGGER.info(f"تم تعيين اسم السورس: {name}")
    return True

async def get_developer_name():
    """الحصول على اسم المطور"""
    bot_id = await get_bot_id()
    if not bot_id:
        return None

    settings_prefix = await get_prefix("settings")
    return await redis_hget(f"{settings_prefix}bot_info", "developer_name")

async def set_developer_name(name):
    """تعيين اسم المطور"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}bot_info", "developer_name", name)
    LOGGER.info(f"تم تعيين اسم المطور: {name}")
    return True

async def get_channel():
    """الحصول على قناة البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        return DEFAULT_CHANNEL

    settings_prefix = await get_prefix("settings")
    channel = await redis_hget(f"{settings_prefix}bot_info", "channel")
    return channel if channel else DEFAULT_CHANNEL

async def set_channel(channel):
    """تعيين قناة البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}bot_info", "channel", channel)
    LOGGER.info(f"تم تعيين قناة البوت: {channel}")
    return True

async def get_group():
    """الحصول على مجموعة البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        return DEFAULT_GROUP

    settings_prefix = await get_prefix("settings")
    group = await redis_hget(f"{settings_prefix}bot_info", "group")
    return group if group else DEFAULT_GROUP

async def set_group(group):
    """تعيين مجموعة البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}bot_info", "group", group)
    LOGGER.info(f"تم تعيين مجموعة البوت: {group}")
    return True


async def is_force_sub_enabled():
    """التحقق مما إذا كان الاشتراك الإجباري مفعلاً"""
    bot_id = await get_bot_id()
    if not bot_id:
        return False

    force_sub_prefix = await get_prefix("force_sub")
    status = await redis_get(force_sub_prefix)
    return status == "enabled"

async def enable_force_sub():
    """تفعيل الاشتراك الإجباري"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    force_sub_prefix = await get_prefix("force_sub")
    await redis_set(force_sub_prefix, "enabled")
    LOGGER.info("تم تفعيل الاشتراك الإجباري")
    return True

async def disable_force_sub():
    """تعطيل الاشتراك الإجباري"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    force_sub_prefix = await get_prefix("force_sub")
    await redis_set(force_sub_prefix, "disabled")
    LOGGER.info("تم تعطيل الاشتراك الإجباري")
    return True


async def is_advanced_perms_enabled():
    """التحقق مما إذا كانت صلاحيات التشغيل المتقدمة مفعلة"""
    bot_id = await get_bot_id()
    if not bot_id:
        return False

    advanced_perms_prefix = await get_prefix("advanced_perms")
    status = await redis_get(advanced_perms_prefix)
    return status == "enabled"

async def enable_advanced_perms():
    """تفعيل صلاحيات التشغيل المتقدمة"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    advanced_perms_prefix = await get_prefix("advanced_perms")
    await redis_set(advanced_perms_prefix, "enabled")
    LOGGER.info("تم تفعيل صلاحيات التشغيل المتقدمة")
    return True

async def disable_advanced_perms():
    """تعطيل صلاحيات التشغيل المتقدمة"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    advanced_perms_prefix = await get_prefix("advanced_perms")
    await redis_set(advanced_perms_prefix, "disabled")
    LOGGER.info("تم تعطيل صلاحيات التشغيل المتقدمة")
    return True


async def is_log_enabled():
    """التحقق مما إذا كان سجل التشغيل مفعلاً"""
    bot_id = await get_bot_id()
    if not bot_id:
        return False

    settings_prefix = await get_prefix("settings")
    status = await redis_hget(f"{settings_prefix}settings", "log_enabled")
    return status == "enabled"

async def enable_log():
    """تفعيل سجل التشغيل"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "log_enabled", "enabled")
    LOGGER.info("تم تفعيل سجل التشغيل")
    return True

async def disable_log():
    """تعطيل سجل التشغيل"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "log_enabled", "disabled")
    LOGGER.info("تم تعطيل سجل التشغيل")
    return True

async def get_log_channel():
    """الحصول على قناة سجل التشغيل"""
    bot_id = await get_bot_id()
    if not bot_id:
        return None

    log_channel_prefix = await get_prefix("log_channel")
    return await redis_get(log_channel_prefix)

async def set_log_channel(channel_id):
    """تعيين قناة سجل التشغيل"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    log_channel_prefix = await get_prefix("log_channel")
    await redis_set(log_channel_prefix, channel_id)
    LOGGER.info(f"تم تعيين قناة سجل التشغيل: {channel_id}")
    return True


async def is_communication_enabled():
    """التحقق مما إذا كان التواصل مفعلاً"""
    bot_id = await get_bot_id()
    if not bot_id:
        return False

    settings_prefix = await get_prefix("settings")
    status = await redis_hget(f"{settings_prefix}settings", "communication_enabled")
    return status == "enabled"

async def enable_communication():
    """تفعيل التواصل"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "communication_enabled", "enabled")
    LOGGER.info("تم تفعيل التواصل")
    return True

async def disable_communication():
    """تعطيل التواصل"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "communication_enabled", "disabled")
    LOGGER.info("تم تعطيل التواصل")
    return True


async def is_youtube_enabled():
    """التحقق مما إذا كان تشغيل اليوتيوب مفعلاً"""
    bot_id = await get_bot_id()
    if not bot_id:
        return True  

    settings_prefix = await get_prefix("settings")
    status = await redis_hget(f"{settings_prefix}settings", "youtube_enabled")
    return status != "disabled"  

async def enable_youtube():
    """تفعيل تشغيل اليوتيوب"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "youtube_enabled", "enabled")
    LOGGER.info("تم تفعيل تشغيل اليوتيوب")
    return True

async def disable_youtube():
    """تعطيل تشغيل اليوتيوب"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "youtube_enabled", "disabled")
    LOGGER.info("تم تعطيل تشغيل اليوتيوب")
    return True


async def is_audio_enabled():
    """التحقق مما إذا كان التشغيل الصوتي مفعلاً"""
    bot_id = await get_bot_id()
    if not bot_id:
        return True  

    settings_prefix = await get_prefix("settings")
    status = await redis_hget(f"{settings_prefix}settings", "audio_enabled")
    return status != "disabled"  

async def enable_audio():
    """تفعيل التشغيل الصوتي"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "audio_enabled", "enabled")
    LOGGER.info("تم تفعيل التشغيل الصوتي")
    return True

async def disable_audio():
    """تعطيل التشغيل الصوتي"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    await redis_hset(f"{settings_prefix}settings", "audio_enabled", "disabled")
    LOGGER.info("تم تعطيل التشغيل الصوتي")
    return True


# أسماء بديلة للوظائف للتوافق مع admin.py
async def set_youtube_enabled(enabled: bool):
    """تعيين حالة تشغيل اليوتيوب"""
    if enabled:
        return await enable_youtube()
    else:
        return await disable_youtube()


async def set_audio_enabled(enabled: bool):
    """تعيين حالة التشغيل الصوتي"""
    if enabled:
        return await enable_audio()
    else:
        return await disable_audio()


async def get_promotion_status():
    """الحصول على حالة الترويج"""
    bot_id = await get_bot_id()
    if not bot_id:
        return None

    promotion_prefix = await get_prefix("promotion")
    return await redis_get(promotion_prefix)

async def set_promotion(promotion_type):
    """تعيين نوع الترويج"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    promotion_prefix = await get_prefix("promotion")
    await redis_set(promotion_prefix, promotion_type)
    LOGGER.info(f"تم تعيين الترويج: {promotion_type}")
    return True

async def clear_promotion():
    """إلغاء الترويج"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    promotion_prefix = await get_prefix("promotion")
    await redis_delete(promotion_prefix)
    LOGGER.info("تم إلغاء الترويج")
    return True


async def add_assistant_account(user_id, session_string, first_name=None, username=None):
    """إضافة حساب مساعد"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    account_data = {
        "user_id": user_id,
        "session_string": session_string,
        "first_name": first_name,
        "username": username,
        "added_at": str(int(time.time()))
    }

    assistant_accounts_prefix = await get_prefix("assistant_accounts")
    await redis_hset(assistant_accounts_prefix, str(user_id), account_data)
    LOGGER.info(f"تم إضافة حساب مساعد جديد: {user_id}")
    return True

async def remove_assistant_account(user_id):
    """إزالة حساب مساعد"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    assistant_accounts_prefix = await get_prefix("assistant_accounts")
    await redis_hdel(assistant_accounts_prefix, str(user_id))
    LOGGER.info(f"تم إزالة الحساب المساعد: {user_id}")
    return True

async def get_assistant_account(user_id):
    """الحصول على معلومات حساب مساعد"""
    bot_id = await get_bot_id()
    if not bot_id:
        return None

    assistant_accounts_prefix = await get_prefix("assistant_accounts")
    return await redis_hget(assistant_accounts_prefix, str(user_id))

async def get_all_assistant_accounts():
    """الحصول على جميع الحسابات المساعدة"""
    bot_id = await get_bot_id()
    if not bot_id:
        return {}

    assistant_accounts_prefix = await get_prefix("assistant_accounts")
    return await redis_hgetall(assistant_accounts_prefix)

async def update_assistant_account(user_id, key, value):
    """تحديث معلومات حساب مساعد"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    account = await get_assistant_account(user_id)
    if not account:
        return False

    account[key] = value
    assistant_accounts_prefix = await get_prefix("assistant_accounts")
    await redis_hset(assistant_accounts_prefix, str(user_id), account)
    LOGGER.info(f"تم تحديث معلومات الحساب المساعد: {user_id}")
    return True


# إعدادات أمر البوت
async def get_bot_status_settings():
    """الحصول على إعدادات أمر البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        return {"reply_with_photo": True, "custom_message": None}

    settings_prefix = await get_prefix("settings")
    reply_with_photo = await redis_hget(f"{settings_prefix}bot_status", "reply_with_photo")
    custom_message = await redis_hget(f"{settings_prefix}bot_status", "custom_message")

    return {
        "reply_with_photo": reply_with_photo != "false",
        "custom_message": custom_message
    }

async def set_bot_status_reply_with_photo(enabled):
    """تعيين إعداد الرد بصورة البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    value = "true" if enabled else "false"
    await redis_hset(f"{settings_prefix}bot_status", "reply_with_photo", value)
    LOGGER.info(f"تم تعيين الرد بصورة البوت: {enabled}")
    return True

async def set_bot_status_custom_message(message):
    """تعيين رسالة مخصصة لأمر البوت"""
    bot_id = await get_bot_id()
    if not bot_id:
        LOGGER.error("لم يتم تعيين معرف البوت بعد")
        return False

    settings_prefix = await get_prefix("settings")
    if message:
        await redis_hset(f"{settings_prefix}bot_status", "custom_message", message)
    else:
        await redis_hdel(f"{settings_prefix}bot_status", "custom_message")
    LOGGER.info(f"تم تعيين رسالة مخصصة لأمر البوت: {message}")
    return True
