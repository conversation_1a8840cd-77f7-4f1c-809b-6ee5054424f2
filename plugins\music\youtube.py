"""
وحدة البحث والتحميل من يوتيوب
تستخدم youtube-search-python للبحث و yt-dlp للتحميل
"""

import os
import asyncio
import aiofiles
import yt_dlp
from typing import Dict, List, Optional, Any
from youtubesearchpython import VideosSearch, Video
from urllib.parse import urlparse, parse_qs


# إعدادات التحميل
DOWNLOAD_DIR = "downloads"
MAX_DURATION = 600  # 10 دقائق كحد أقصى
AUDIO_QUALITY = "bestaudio/best"


async def ensure_download_dir():
    """التأكد من وجود مجلد التحميل"""
    if not os.path.exists(DOWNLOAD_DIR):
        os.makedirs(DOWNLOAD_DIR)


def is_youtube_url(url: str) -> bool:
    """التحقق من أن الرابط من يوتيوب"""
    try:
        parsed = urlparse(url)
        return parsed.netloc in ['www.youtube.com', 'youtube.com', 'youtu.be', 'm.youtube.com']
    except:
        return False


def extract_video_id(url: str) -> Optional[str]:
    """استخراج معرف الفيديو من رابط يوتيوب"""
    try:
        parsed = urlparse(url)
        
        if parsed.netloc == 'youtu.be':
            return parsed.path[1:]
        elif parsed.netloc in ['www.youtube.com', 'youtube.com', 'm.youtube.com']:
            if parsed.path == '/watch':
                return parse_qs(parsed.query).get('v', [None])[0]
            elif parsed.path.startswith('/embed/'):
                return parsed.path.split('/')[2]
        
        return None
    except:
        return None


async def search_youtube(query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """البحث في يوتيوب"""
    try:
        # تشغيل البحث في thread منفصل
        def search_sync():
            search = VideosSearch(query, limit=limit)
            return search.result()
        
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, search_sync)
        
        videos = []
        for video in result.get('result', []):
            # تحويل المدة إلى ثواني
            duration_str = video.get('duration', '0:00')
            duration_parts = duration_str.split(':')
            if len(duration_parts) == 2:
                duration_seconds = int(duration_parts[0]) * 60 + int(duration_parts[1])
            elif len(duration_parts) == 3:
                duration_seconds = int(duration_parts[0]) * 3600 + int(duration_parts[1]) * 60 + int(duration_parts[2])
            else:
                duration_seconds = 0
            
            # تخطي الفيديوهات الطويلة جداً
            if duration_seconds > MAX_DURATION:
                continue
            
            video_data = {
                'id': video.get('id'),
                'title': video.get('title'),
                'duration': video.get('duration'),
                'duration_seconds': duration_seconds,
                'views': video.get('viewCount', {}).get('text', '0'),
                'channel': video.get('channel', {}).get('name', 'غير معروف'),
                'thumbnail': video.get('thumbnails', [{}])[-1].get('url', ''),
                'url': video.get('link'),
                'description': video.get('descriptionSnippet', [{}])[0].get('text', '') if video.get('descriptionSnippet') else ''
            }
            videos.append(video_data)
        
        return videos
    
    except Exception as e:
        print(f"خطأ في البحث: {e}")
        return []


async def get_video_info(url: str) -> Optional[Dict[str, Any]]:
    """الحصول على معلومات الفيديو"""
    try:
        def get_info_sync():
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                return ydl.extract_info(url, download=False)
        
        loop = asyncio.get_event_loop()
        info = await loop.run_in_executor(None, get_info_sync)
        
        if not info:
            return None
        
        # التحقق من المدة
        duration = info.get('duration', 0)
        if duration > MAX_DURATION:
            return None
        
        video_data = {
            'id': info.get('id'),
            'title': info.get('title'),
            'duration': f"{duration // 60}:{duration % 60:02d}" if duration else "0:00",
            'duration_seconds': duration,
            'views': info.get('view_count', 0),
            'channel': info.get('uploader', 'غير معروف'),
            'thumbnail': info.get('thumbnail', ''),
            'url': url,
            'description': info.get('description', '')[:200] + '...' if info.get('description') else ''
        }
        
        return video_data
    
    except Exception as e:
        print(f"خطأ في الحصول على معلومات الفيديو: {e}")
        return None


async def download_audio(url: str, chat_id: int) -> Optional[str]:
    """تحميل الصوت من يوتيوب"""
    try:
        await ensure_download_dir()
        
        # إعدادات yt-dlp
        ydl_opts = {
            'format': AUDIO_QUALITY,
            'outtmpl': f'{DOWNLOAD_DIR}/%(title)s_%(id)s.%(ext)s',
            'extractaudio': True,
            'audioformat': 'mp3',
            'audioquality': '192K',
            'quiet': True,
            'no_warnings': True,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }]
        }
        
        def download_sync():
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                return info
        
        loop = asyncio.get_event_loop()
        info = await loop.run_in_executor(None, download_sync)
        
        if not info:
            return None
        
        # البحث عن الملف المحمل
        video_id = info.get('id')
        title = info.get('title', 'unknown')
        
        # تنظيف اسم الملف
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        
        # البحث عن الملف
        for ext in ['mp3', 'webm', 'm4a', 'ogg']:
            filename = f"{safe_title}_{video_id}.{ext}"
            filepath = os.path.join(DOWNLOAD_DIR, filename)
            if os.path.exists(filepath):
                return filepath
        
        # البحث بطريقة أخرى
        for file in os.listdir(DOWNLOAD_DIR):
            if video_id in file and any(file.endswith(ext) for ext in ['mp3', 'webm', 'm4a', 'ogg']):
                return os.path.join(DOWNLOAD_DIR, file)
        
        return None
    
    except Exception as e:
        print(f"خطأ في تحميل الصوت: {e}")
        return None


async def download_video_info(url: str) -> Optional[Dict[str, Any]]:
    """تحميل معلومات الفيديو مع الصوت"""
    try:
        # الحصول على معلومات الفيديو
        video_info = await get_video_info(url)
        if not video_info:
            return None
        
        # تحميل الصوت
        audio_path = await download_audio(url, 0)  # استخدام 0 كـ chat_id مؤقت
        if not audio_path:
            return None
        
        video_info['audio_path'] = audio_path
        video_info['file_size'] = os.path.getsize(audio_path) if os.path.exists(audio_path) else 0
        
        return video_info
    
    except Exception as e:
        print(f"خطأ في تحميل معلومات الفيديو: {e}")
        return None


async def cleanup_old_files(max_age_hours: int = 24):
    """تنظيف الملفات القديمة"""
    try:
        if not os.path.exists(DOWNLOAD_DIR):
            return
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        for filename in os.listdir(DOWNLOAD_DIR):
            filepath = os.path.join(DOWNLOAD_DIR, filename)
            if os.path.isfile(filepath):
                file_age = current_time - os.path.getmtime(filepath)
                if file_age > max_age_seconds:
                    try:
                        os.remove(filepath)
                        print(f"تم حذف الملف القديم: {filename}")
                    except:
                        pass
    
    except Exception as e:
        print(f"خطأ في تنظيف الملفات: {e}")


async def get_playlist_videos(playlist_url: str, limit: int = 50) -> List[Dict[str, Any]]:
    """الحصول على فيديوهات قائمة التشغيل"""
    try:
        def get_playlist_sync():
            ydl_opts = {
                'quiet': True,
                'extract_flat': True,
                'playlistend': limit
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                return ydl.extract_info(playlist_url, download=False)
        
        loop = asyncio.get_event_loop()
        playlist_info = await loop.run_in_executor(None, get_playlist_sync)
        
        if not playlist_info or 'entries' not in playlist_info:
            return []
        
        videos = []
        for entry in playlist_info['entries']:
            if entry and entry.get('id'):
                video_url = f"https://www.youtube.com/watch?v={entry['id']}"
                video_info = await get_video_info(video_url)
                if video_info:
                    videos.append(video_info)
        
        return videos
    
    except Exception as e:
        print(f"خطأ في الحصول على قائمة التشغيل: {e}")
        return []


def format_duration(seconds: int) -> str:
    """تنسيق المدة"""
    if seconds < 3600:
        return f"{seconds // 60}:{seconds % 60:02d}"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours}:{minutes:02d}:{seconds:02d}"


def format_views(views: int) -> str:
    """تنسيق عدد المشاهدات"""
    if views >= 1000000:
        return f"{views / 1000000:.1f}M"
    elif views >= 1000:
        return f"{views / 1000:.1f}K"
    else:
        return str(views)
