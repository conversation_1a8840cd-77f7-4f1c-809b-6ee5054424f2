import os
import sys
from pyrogram import Client, filters
from pyrogram.types import Message, ReplyKeyboardMarkup, KeyboardButton, ReplyKeyboardRemove
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.settings_db import (
    add_assistant_account, remove_assistant_account,
    get_all_assistant_accounts, update_assistant_account
)
from config import API_ID, API_HASH


user_states = {}


def ensure_temp_photos_dir():
    """إنشاء مجلد الصور المؤقت إذا لم يكن موجوداً"""
    temp_dir = "temp_photos"
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    return temp_dir


def check_url_download_dependencies():
    """التحقق من توفر مكتبات تحميل الصور من الروابط"""
    try:
        import aiohttp
        import aiofiles
        return True
    except ImportError:
        return False


async def assistant_main_keyboard():
    """لوحة مفاتيح إدارة المساعد الرئيسية"""
    assistants = await get_all_assistant_accounts()
    
    if assistants:
        buttons = [
            [KeyboardButton("👤 معلومات المساعد"), KeyboardButton("🔄 تحديث المعلومات")],
            [KeyboardButton("✏️ تعديل الاسم الأول"), KeyboardButton("✏️ تعديل الاسم الثاني")],
            [KeyboardButton("✏️ تعديل البايو"), KeyboardButton("✏️ تعديل اسم المستخدم")],
            [KeyboardButton("🖼 إدارة الصورة الشخصية"), KeyboardButton("👥 إدارة المجموعات")],
            [KeyboardButton("🗑 حذف الحساب المساعد")],
            [KeyboardButton("🔙 رجوع للوحة المطور")]
        ]
    else:
        buttons = [
            [KeyboardButton("➕ إضافة حساب مساعد جديد")],
            [KeyboardButton("🔙 رجوع للوحة المطور")]
        ]
    
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def assistant_photo_keyboard():
    """لوحة مفاتيح إدارة الصورة الشخصية"""
    buttons = [
        [KeyboardButton("📷 رفع صورة جديدة"), KeyboardButton("🔗 إضافة صورة برابط")],
        [KeyboardButton("🗑 حذف الصورة الشخصية")],
        [KeyboardButton("🔙 رجوع لإدارة المساعد")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def assistant_groups_keyboard():
    """لوحة مفاتيح إدارة المجموعات"""
    buttons = [
        [KeyboardButton("👋 دعوة للمجموعة الحالية"), KeyboardButton("🔗 دعوة برابط")],
        [KeyboardButton("🚪 خروج من المجموعة الحالية"), KeyboardButton("🚪 خروج من جميع المجموعات")],
        [KeyboardButton("📋 عرض المجموعات المنضم إليها")],
        [KeyboardButton("🔙 رجوع لإدارة المساعد")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def dev_main_keyboard():
    """لوحة مفاتيح المطور الرئيسية المحسنة"""
    buttons = [
        [KeyboardButton("📊 الإحصائيات"), KeyboardButton("📢 الإذاعات")],
        [KeyboardButton("⚙️ إعدادات البوت"), KeyboardButton("👤 إدارة المساعد")],
        [KeyboardButton("🚫 إدارة الحظر"), KeyboardButton("👥 إدارة الرتب")],
        [KeyboardButton("🤖 إعدادات أمر البوت"), KeyboardButton("📝 سجل التشغيل")],
        [KeyboardButton("🔧 أدوات المطور"), KeyboardButton("❌ إخفاء الكيبورد")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def bot_settings_main_keyboard():
    """لوحة مفاتيح إعدادات البوت الرئيسية"""
    buttons = [
        [KeyboardButton("🏷 إعدادات الهوية"), KeyboardButton("🔗 إعدادات الروابط")],
        [KeyboardButton("🔒 إعدادات الأمان"), KeyboardButton("🎵 إعدادات التشغيل")],
        [KeyboardButton("📢 إعدادات الإذاعة"), KeyboardButton("💬 إعدادات التواصل")],
        [KeyboardButton("🔙 رجوع للوحة المطور")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def identity_settings_keyboard():
    """لوحة مفاتيح إعدادات الهوية"""
    buttons = [
        [KeyboardButton("✏️ تعيين اسم البوت"), KeyboardButton("✏️ تعيين اسم السورس")],
        [KeyboardButton("✏️ تعيين اسم المطور"), KeyboardButton("📝 عرض معلومات البوت")],
        [KeyboardButton("🔙 رجوع لإعدادات البوت")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def links_settings_keyboard():
    """لوحة مفاتيح إعدادات الروابط"""
    buttons = [
        [KeyboardButton("📢 تعيين قناة البوت"), KeyboardButton("👥 تعيين مجموعة البوت")],
        [KeyboardButton("🔗 عرض الروابط الحالية"), KeyboardButton("🧪 اختبار الروابط")],
        [KeyboardButton("🔙 رجوع لإعدادات البوت")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def security_settings_keyboard():
    """لوحة مفاتيح إعدادات الأمان"""
    buttons = [
        [KeyboardButton("🔄 الاشتراك الإجباري"), KeyboardButton("🔐 صلاحيات التشغيل")],
        [KeyboardButton("🛡 حماية المجموعات"), KeyboardButton("⚡ الحماية المتقدمة")],
        [KeyboardButton("📊 عرض حالة الأمان"), KeyboardButton("🔙 رجوع لإعدادات البوت")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def broadcast_settings_keyboard():
    """لوحة مفاتيح إعدادات الإذاعة"""
    buttons = [
        [KeyboardButton("📢 إضافة قناة للإذاعة"), KeyboardButton("🗑 حذف قناة من الإذاعة")],
        [KeyboardButton("📋 قائمة القنوات"), KeyboardButton("🧪 اختبار الإذاعة")],
        [KeyboardButton("⚙️ إعدادات الإذاعة المتقدمة"), KeyboardButton("🔙 رجوع لإعدادات البوت")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def ban_management_keyboard():
    """لوحة مفاتيح إدارة الحظر المحسنة"""
    buttons = [
        [KeyboardButton("🚫 حظر مستخدم"), KeyboardButton("✅ إلغاء حظر مستخدم")],
        [KeyboardButton("🔇 كتم مستخدم"), KeyboardButton("🔊 إلغاء كتم مستخدم")],
        [KeyboardButton("🚫 حظر مجموعة"), KeyboardButton("✅ إلغاء حظر مجموعة")],
        [KeyboardButton("📋 قائمة المحظورين"), KeyboardButton("📋 قائمة المكتومين")],
        [KeyboardButton("🔙 رجوع للوحة المطور")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def rank_management_keyboard():
    """لوحة مفاتيح إدارة الرتب"""
    buttons = [
        [KeyboardButton("👨‍💻 إضافة مطور"), KeyboardButton("❌ حذف مطور")],
        [KeyboardButton("👮‍♂️ إضافة مشرف"), KeyboardButton("❌ حذف مشرف")],
        [KeyboardButton("⭐ إضافة VIP"), KeyboardButton("❌ حذف VIP")],
        [KeyboardButton("📋 قائمة المطورين"), KeyboardButton("📋 قائمة المشرفين")],
        [KeyboardButton("📋 قائمة VIP"), KeyboardButton("🔙 رجوع للوحة المطور")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def developer_tools_keyboard():
    """لوحة مفاتيح أدوات المطور"""
    buttons = [
        [KeyboardButton("🔄 إعادة تشغيل البوت"), KeyboardButton("📊 معلومات النظام")],
        [KeyboardButton("🗂 تنظيف قاعدة البيانات"), KeyboardButton("💾 نسخ احتياطي")],
        [KeyboardButton("🧪 اختبار الاتصالات"), KeyboardButton("📈 تقرير الأداء")],
        [KeyboardButton("🔙 رجوع للوحة المطور")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def broadcast_main_keyboard():
    """لوحة مفاتيح الإذاعات الرئيسية"""
    buttons = [
        [KeyboardButton("📢 إذاعة عامة"), KeyboardButton("👤 إذاعة للمستخدمين")],
        [KeyboardButton("🏘 إذاعة للمجموعات"), KeyboardButton("📺 إذاعة للقنوات")],
        [KeyboardButton("📊 إحصائيات الإذاعة"), KeyboardButton("⚙️ إعدادات الإذاعة")],
        [KeyboardButton("🔙 رجوع للوحة المطور")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def cancel_keyboard():
    """لوحة مفاتيح الإلغاء"""
    buttons = [
        [KeyboardButton("❌ إلغاء العملية")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)

async def confirm_keyboard():
    """لوحة مفاتيح التأكيد"""
    buttons = [
        [KeyboardButton("✅ نعم، تأكيد"), KeyboardButton("❌ لا، إلغاء")]
    ]
    return ReplyKeyboardMarkup(buttons, resize_keyboard=True, one_time_keyboard=False)


@Client.on_message(command_handler(["إدارة المساعد", "مساعد كيبورد", "لوحة المساعد"]) & filters.private & dev)
@dev_only
async def assistant_management_start(client: Client, message: Message):
    """معالجة أمر بدء إدارة المساعد"""
    
    assistants = await get_all_assistant_accounts()
    
    if assistants:
        user_id = list(assistants.keys())[0]
        account = assistants[user_id]
        
        
        status = "✅ متصل" if hasattr(client, 'assistant') and client.assistant else "❌ غير متصل"
        
        text = (
            f"👤 **إدارة الحساب المساعد**\n\n"
            f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
            f"**المعرف:** `{user_id}`\n"
            f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n"
            f"**الحالة:** {status}\n\n"
            f"🎛 **استخدم الأزرار أدناه لإدارة الحساب المساعد:**"
        )
    else:
        text = (
            f"👤 **إدارة الحساب المساعد**\n\n"
            f"⚠️ **لا يوجد حساب مساعد مضاف حالياً.**\n\n"
            f"يمكنك إضافة حساب مساعد جديد باستخدام الزر أدناه.\n\n"
            f"🎛 **استخدم الأزرار أدناه للبدء:**"
        )

    await message.reply_text(
        text,
        reply_markup=await assistant_main_keyboard()
    )


@Client.on_message(command_handler(["لوحة المطور", "مطور كيبورد", "dev panel"]) & filters.private & dev)
@dev_only
async def dev_panel_start(client: Client, message: Message):
    """معالجة أمر لوحة المطور"""
    text = (
        f"👨‍💻 **لوحة تحكم المطور**\n\n"
        f"🎛 **استخدم الأزرار أدناه للوصول لجميع أدوات المطور:**\n\n"
        f"📊 **الإحصائيات** - عرض إحصائيات البوت\n"
        f"📢 **الإذاعات** - إرسال رسائل جماعية\n"
        f"⚙️ **إعدادات البوت** - تخصيص إعدادات البوت\n"
        f"👤 **إدارة المساعد** - إدارة الحساب المساعد\n"
        f"📝 **سجل التشغيل** - إعدادات السجلات\n"
        f"🚫 **الحظر** - إدارة المستخدمين المحظورين"
    )

    await message.reply_text(
        text,
        reply_markup=await dev_main_keyboard()
    )


@Client.on_message((filters.text | filters.photo) & filters.private & dev, group=1)
@dev_only
async def handle_user_states(client: Client, message: Message):
    """معالجة حالات المستخدم والمدخلات التفاعلية"""
    user_id = message.from_user.id

    
    if user_id in user_states:
        await handle_user_input(client, message)
        return  


@Client.on_message(filters.text & filters.private & dev, group=2)
@dev_only
async def handle_main_assistant_buttons(client: Client, message: Message):
    """معالجة الأزرار الرئيسية لإدارة المساعد"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["👤 إدارة المساعد", "👤 معلومات المساعد", "🔄 تحديث المعلومات",
                "➕ إضافة حساب مساعد جديد", "🗑 حذف الحساب المساعد"]:

        if text == "👤 إدارة المساعد":
            await assistant_management_start(client, message)
        elif text == "👤 معلومات المساعد":
            await show_assistant_info(client, message)
        elif text == "🔄 تحديث المعلومات":
            await refresh_assistant_info(client, message)
        elif text == "➕ إضافة حساب مساعد جديد":
            await start_add_assistant(client, message)
        elif text == "🗑 حذف الحساب المساعد":
            await confirm_delete_assistant(client, message)


@Client.on_message(filters.text & filters.private & dev, group=3)
@dev_only
async def handle_profile_edit_buttons(client: Client, message: Message):
    """معالجة أزرار تعديل الملف الشخصي"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["✏️ تعديل الاسم الأول", "✏️ تعديل الاسم الثاني", "✏️ تعديل البايو", "✏️ تعديل اسم المستخدم"]:

        if text == "✏️ تعديل الاسم الأول":
            await start_edit_first_name(client, message)
        elif text == "✏️ تعديل الاسم الثاني":
            await start_edit_last_name(client, message)
        elif text == "✏️ تعديل البايو":
            await start_edit_bio(client, message)
        elif text == "✏️ تعديل اسم المستخدم":
            await start_edit_username(client, message)


@Client.on_message(filters.text & filters.private & dev, group=4)
@dev_only
async def handle_photo_management_buttons(client: Client, message: Message):
    """معالجة أزرار إدارة الصورة الشخصية"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["🖼 إدارة الصورة الشخصية", "📷 رفع صورة جديدة", "🔗 إضافة صورة برابط", "🗑 حذف الصورة الشخصية"]:

        if text == "🖼 إدارة الصورة الشخصية":
            await show_photo_management(client, message)

        elif text == "📷 رفع صورة جديدة":
            
            if not hasattr(client, 'assistant') or not client.assistant:
                await message.reply_text(
                    "⚠️ **لا يوجد حساب مساعد نشط.**",
                    reply_markup=await assistant_photo_keyboard()
                )
                return

            user_states[user_id] = "waiting_photo"

            await message.reply_text(
                "📷 **رفع صورة شخصية جديدة**\n\n"
                "يرجى إرسال الصورة التي تريد استخدامها كصورة شخصية للمساعد.\n\n"
                "⚠️ **ملاحظة:** يجب أن تكون الصورة بصيغة JPG أو PNG.",
                reply_markup=await cancel_keyboard()
            )

        elif text == "🔗 إضافة صورة برابط":
            
            if not hasattr(client, 'assistant') or not client.assistant:
                await message.reply_text(
                    "⚠️ **لا يوجد حساب مساعد نشط.**",
                    reply_markup=await assistant_photo_keyboard()
                )
                return

            user_states[user_id] = "waiting_photo_url"

            await message.reply_text(
                "🔗 **إضافة صورة برابط**\n\n"
                "يرجى إرسال رابط الصورة التي تريد استخدامها كصورة شخصية للمساعد.\n\n"
                "⚠️ **ملاحظة:** يجب أن يكون الرابط مباشر للصورة.",
                reply_markup=await cancel_keyboard()
            )

        elif text == "🗑 حذف الصورة الشخصية":
            await delete_profile_photo(client, message)


@Client.on_message(filters.text & filters.private & dev, group=5)
@dev_only
async def handle_groups_management_buttons(client: Client, message: Message):
    """معالجة أزرار إدارة المجموعات"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["👥 إدارة المجموعات", "🔗 دعوة برابط", "🚪 خروج من جميع المجموعات", "📋 عرض المجموعات المنضم إليها"]:

        if text == "👥 إدارة المجموعات":
            await show_groups_management(client, message)

        elif text == "🔗 دعوة برابط":
            
            if not hasattr(client, 'assistant') or not client.assistant:
                await message.reply_text(
                    "⚠️ **لا يوجد حساب مساعد نشط.**",
                    reply_markup=await assistant_groups_keyboard()
                )
                return

            user_states[user_id] = "waiting_invite_link"

            await message.reply_text(
                "🔗 **دعوة المساعد برابط**\n\n"
                "يرجى إرسال رابط الدعوة للمجموعة التي تريد دعوة المساعد إليها.\n\n"
                "**مثال:** `https://t.me/+xxxxxxxxx`",
                reply_markup=await cancel_keyboard()
            )

        elif text == "🚪 خروج من جميع المجموعات":
            await leave_all_groups(client, message)

        elif text == "📋 عرض المجموعات المنضم إليها":
            await list_joined_groups(client, message)


@Client.on_message(filters.text & filters.private & dev, group=6)
@dev_only
async def handle_dev_main_buttons(client: Client, message: Message):
    """معالجة أزرار لوحة المطور الرئيسية"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["📊 الإحصائيات", "📢 الإذاعات", "⚙️ إعدادات البوت", "🚫 إدارة الحظر",
                "👥 إدارة الرتب", "🤖 إعدادات أمر البوت", "📝 سجل التشغيل", "🔧 أدوات المطور"]:

        if text == "📊 الإحصائيات":
            await show_bot_statistics(client, message)
        elif text == "📢 الإذاعات":
            await show_broadcast_menu(client, message)
        elif text == "⚙️ إعدادات البوت":
            await show_bot_settings_menu(client, message)
        elif text == "🚫 إدارة الحظر":
            await show_ban_management_menu(client, message)
        elif text == "👥 إدارة الرتب":
            await show_rank_management_menu(client, message)
        elif text == "🤖 إعدادات أمر البوت":
            from plugins.dev.bot_status_settings import bot_status_settings_menu
            await bot_status_settings_menu(client, message)
        elif text == "📝 سجل التشغيل":
            await show_log_settings_menu(client, message)
        elif text == "🔧 أدوات المطور":
            await show_developer_tools_menu(client, message)


@Client.on_message(filters.text & filters.private & dev, group=7)
@dev_only
async def handle_bot_settings_buttons(client: Client, message: Message):
    """معالجة أزرار إعدادات البوت"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    if text in ["🏷 إعدادات الهوية", "🔗 إعدادات الروابط", "🔒 إعدادات الأمان",
                "🎵 إعدادات التشغيل", "📢 إعدادات الإذاعة", "💬 إعدادات التواصل"]:

        if text == "🏷 إعدادات الهوية":
            await show_identity_settings(client, message)
        elif text == "🔗 إعدادات الروابط":
            await show_links_settings(client, message)
        elif text == "🔒 إعدادات الأمان":
            await show_security_settings(client, message)
        elif text == "🎵 إعدادات التشغيل":
            await show_music_settings(client, message)
        elif text == "📢 إعدادات الإذاعة":
            await show_broadcast_settings(client, message)
        elif text == "💬 إعدادات التواصل":
            await show_communication_settings(client, message)


@Client.on_message(filters.text & filters.private & dev, group=8)
@dev_only
async def handle_navigation_buttons(client: Client, message: Message):
    """معالجة أزرار التنقل والتحكم"""
    text = message.text
    user_id = message.from_user.id

    
    if user_id in user_states:
        return

    navigation_buttons = [
        "🔙 رجوع للوحة المطور", "🔙 رجوع لإدارة المساعد", "🔙 رجوع لإعدادات البوت",
        "🔙 رجوع لإعدادات الهوية", "🔙 رجوع لإعدادات الروابط", "🔙 رجوع لإعدادات الأمان",
        "🔙 رجوع لإعدادات الإذاعة", "❌ إخفاء الكيبورد", "❌ إلغاء العملية"
    ]

    if text in navigation_buttons:
        if text == "🔙 رجوع للوحة المطور":
            await dev_panel_start(client, message)
        elif text == "🔙 رجوع لإدارة المساعد":
            await assistant_management_start(client, message)
        elif text == "🔙 رجوع لإعدادات البوت":
            await show_bot_settings_menu(client, message)
        elif text == "🔙 رجوع لإعدادات الهوية":
            await show_identity_settings(client, message)
        elif text == "🔙 رجوع لإعدادات الروابط":
            await show_links_settings(client, message)
        elif text == "🔙 رجوع لإعدادات الأمان":
            await show_security_settings(client, message)
        elif text == "🔙 رجوع لإعدادات الإذاعة":
            await show_broadcast_settings(client, message)
        elif text == "❌ إخفاء الكيبورد":
            await message.reply_text(
                "✅ **تم إخفاء الكيبورد بنجاح!**\n\n"
                "يمكنك استخدام الأوامر النصية أو كتابة `/start` لإظهار الكيبورد مرة أخرى.",
                reply_markup=ReplyKeyboardRemove()
            )
        elif text == "❌ إلغاء العملية":
            if user_id in user_states:
                del user_states[user_id]
            await assistant_management_start(client, message)


@Client.on_message(filters.text & filters.private & dev, group=10)
@dev_only
async def handle_unknown_buttons(client: Client, message: Message):
    """معالج احتياطي للأزرار غير المعروفة"""
    user_id = message.from_user.id

    
    if user_id in user_states:
        return


async def show_assistant_info(client: Client, message: Message):
    """عرض معلومات المساعد"""
    assistants = await get_all_assistant_accounts()
    
    if not assistants:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد مضاف.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_id = list(assistants.keys())[0]
    account = assistants[user_id]
    
    
    status = "✅ متصل" if hasattr(client, 'assistant') and client.assistant else "❌ غير متصل"
    
    
    additional_info = ""
    if hasattr(client, 'assistant') and client.assistant:
        try:
            me = await client.assistant.get_me()
            additional_info = (
                f"**الاسم الكامل:** {me.first_name} {me.last_name or ''}\n"
                f"**البايو:** {me.bio or 'لا يوجد'}\n"
                f"**رقم الهاتف:** {me.phone_number or 'مخفي'}\n"
                f"**تاريخ الإضافة:** {account.get('added_at', 'غير معروف')}\n"
            )
        except:
            additional_info = "**لا يمكن الحصول على معلومات إضافية (المساعد غير متصل)**\n"

    text = (
        f"👤 **معلومات مفصلة عن الحساب المساعد**\n\n"
        f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
        f"**المعرف:** `{user_id}`\n"
        f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n"
        f"**الحالة:** {status}\n\n"
        f"{additional_info}"
    )

    await message.reply_text(
        text,
        reply_markup=await assistant_main_keyboard()
    )

async def refresh_assistant_info(client: Client, message: Message):
    """تحديث معلومات المساعد"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    try:
        
        me = await client.assistant.get_me()

        
        await update_assistant_account(me.id, "first_name", me.first_name)
        if me.username:
            await update_assistant_account(me.id, "username", me.username)

        await message.reply_text(
            "✅ **تم تحديث معلومات المساعد بنجاح!**",
            reply_markup=await assistant_main_keyboard()
        )

        
        await show_assistant_info(client, message)

    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث المعلومات:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )


async def show_photo_management(client: Client, message: Message):
    """عرض إدارة الصورة الشخصية"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    text = (
        f"🖼 **إدارة الصورة الشخصية للمساعد**\n\n"
        f"اختر العملية التي تريد تنفيذها:"
    )

    await message.reply_text(
        text,
        reply_markup=await assistant_photo_keyboard()
    )

async def show_groups_management(client: Client, message: Message):
    """عرض إدارة المجموعات"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    text = (
        f"👥 **إدارة المجموعات للحساب المساعد**\n\n"
        f"اختر العملية التي تريد تنفيذها:"
    )

    await message.reply_text(
        text,
        reply_markup=await assistant_groups_keyboard()
    )


async def start_add_assistant(client: Client, message: Message):
    """بدء عملية إضافة حساب مساعد"""
    
    assistants = await get_all_assistant_accounts()
    if assistants:
        await message.reply_text(
            "⚠️ **يوجد حساب مساعد بالفعل!**\n\n"
            "يجب حذف الحساب الحالي أولاً قبل إضافة حساب جديد.",
            reply_markup=await assistant_main_keyboard()
        )
        return

    
    user_states[message.from_user.id] = "waiting_phone"

    await message.reply_text(
        "📱 **إضافة حساب مساعد جديد**\n\n"
        "يرجى إرسال رقم الهاتف للحساب المساعد.\n\n"
        "**مثال:** `+**********`\n\n"
        "⚠️ **تأكد من أن الرقم صحيح ومتاح للاستخدام.**",
        reply_markup=await cancel_keyboard()
    )

async def start_edit_first_name(client: Client, message: Message):
    """بدء تعديل الاسم الأول"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_states[message.from_user.id] = "waiting_first_name"

    await message.reply_text(
        "✏️ **تعديل الاسم الأول للمساعد**\n\n"
        "يرجى إرسال الاسم الأول الجديد للحساب المساعد.\n\n"
        "⚠️ **ملاحظة:** يمكن تغيير الاسم الأول مرة واحدة كل 60 يوماً في التليجرام.",
        reply_markup=await cancel_keyboard()
    )

async def start_edit_last_name(client: Client, message: Message):
    """بدء تعديل الاسم الثاني"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_states[message.from_user.id] = "waiting_last_name"

    await message.reply_text(
        "✏️ **تعديل الاسم الثاني للمساعد**\n\n"
        "يرجى إرسال الاسم الثاني الجديد للحساب المساعد.\n\n"
        "💡 **نصيحة:** يمكنك إرسال كلمة 'حذف' لحذف الاسم الثاني.",
        reply_markup=await cancel_keyboard()
    )

async def start_edit_bio(client: Client, message: Message):
    """بدء تعديل البايو"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_states[message.from_user.id] = "waiting_bio"

    await message.reply_text(
        "✏️ **تعديل البايو للمساعد**\n\n"
        "يرجى إرسال البايو الجديد للحساب المساعد.\n\n"
        "💡 **نصيحة:** يمكنك إرسال كلمة 'حذف' لحذف البايو.\n"
        "⚠️ **ملاحظة:** الحد الأقصى للبايو هو 70 حرف.",
        reply_markup=await cancel_keyboard()
    )

async def start_edit_username(client: Client, message: Message):
    """بدء تعديل اسم المستخدم"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_states[message.from_user.id] = "waiting_username"

    await message.reply_text(
        "✏️ **تعديل اسم المستخدم للمساعد**\n\n"
        "يرجى إرسال اسم المستخدم الجديد للحساب المساعد.\n\n"
        "💡 **نصيحة:** يمكنك إرسال كلمة 'حذف' لحذف اسم المستخدم.\n"
        "⚠️ **ملاحظة:** يجب أن يبدأ اسم المستخدم بحرف وأن يحتوي على 5-32 حرف.",
        reply_markup=await cancel_keyboard()
    )

async def confirm_delete_assistant(client: Client, message: Message):
    """تأكيد حذف الحساب المساعد"""
    
    assistants = await get_all_assistant_accounts()

    if not assistants:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد لحذفه.**",
            reply_markup=await assistant_main_keyboard()
        )
        return

    user_id = list(assistants.keys())[0]
    account = assistants[user_id]

    user_states[message.from_user.id] = "confirm_delete"

    text = (
        f"🗑 **تأكيد حذف الحساب المساعد**\n\n"
        f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
        f"**المعرف:** `{user_id}`\n"
        f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n\n"
        f"⚠️ **تحذير:** سيتم حذف الحساب المساعد نهائياً من قاعدة البيانات.\n\n"
        f"هل أنت متأكد من أنك تريد المتابعة؟"
    )

    await message.reply_text(
        text,
        reply_markup=await confirm_keyboard()
    )


async def handle_user_input(client: Client, message: Message):
    """معالجة مدخلات المستخدم حسب الحالة"""
    user_id = message.from_user.id
    state = user_states.get(user_id)

    if state == "waiting_phone":
        await handle_phone_input(client, message)
    elif state == "waiting_code":
        await handle_code_input(client, message)
    elif state == "waiting_password":
        await handle_password_input(client, message)
    elif state == "waiting_first_name":
        await handle_first_name_input(client, message)
    elif state == "waiting_last_name":
        await handle_last_name_input(client, message)
    elif state == "waiting_bio":
        await handle_bio_input(client, message)
    elif state == "waiting_username":
        await handle_username_input(client, message)
    elif state == "confirm_delete":
        await handle_delete_confirmation(client, message)
    elif state == "waiting_photo":
        await handle_photo_input(client, message)
    elif state == "waiting_photo_url":
        await handle_photo_url_input(client, message)
    elif state == "waiting_invite_link":
        await handle_invite_link_input(client, message)

async def handle_phone_input(client: Client, message: Message):
    """معالجة إدخال رقم الهاتف"""
    phone_number = message.text.strip()
    user_id = message.from_user.id

    
    if not phone_number.startswith('+') or len(phone_number) < 10:
        await message.reply_text(
            "❌ **رقم الهاتف غير صحيح!**\n\n"
            "يجب أن يبدأ الرقم بـ + ويحتوي على رمز الدولة.\n\n"
            "**مثال:** `+**********`",
            reply_markup=await cancel_keyboard()
        )
        return

    
    try:
        
        assistant = Client(
            f"assistant_{phone_number.replace('+', '')}",
            api_id=API_ID,
            api_hash=API_HASH,
            phone_number=phone_number,
            in_memory=False
        )

        
        await assistant.connect()

        
        sent_code = await assistant.send_code(phone_number)

        
        user_states[user_id] = "waiting_code"
        user_states[f"{user_id}_assistant"] = assistant
        user_states[f"{user_id}_phone"] = phone_number
        user_states[f"{user_id}_code_hash"] = sent_code.phone_code_hash

        await message.reply_text(
            f"📱 **تم إرسال رمز التحقق إلى الرقم {phone_number}**\n\n"
            f"🔢 **يرجى إرسال رمز التحقق.**",
            reply_markup=await cancel_keyboard()
        )

    except Exception as e:
        if user_id in user_states:
            del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء إرسال رمز التحقق:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_code_input(client: Client, message: Message):
    """معالجة إدخال رمز التحقق"""
    code = message.text.strip()
    user_id = message.from_user.id

    try:
        assistant = user_states.get(f"{user_id}_assistant")
        phone_number = user_states.get(f"{user_id}_phone")
        code_hash = user_states.get(f"{user_id}_code_hash")

        if not all([assistant, phone_number, code_hash]):
            raise Exception("معلومات الجلسة مفقودة")

        
        await assistant.sign_in(phone_number, code_hash, code)

        
        me = await assistant.get_me()

        
        session_string = await assistant.export_session_string()

        
        await add_assistant_account(
            me.id,
            session_string,
            me.first_name,
            me.username
        )

        
        await assistant.disconnect()

        
        keys_to_delete = [user_id, f"{user_id}_assistant", f"{user_id}_phone", f"{user_id}_code_hash"]
        for key in keys_to_delete:
            if key in user_states:
                del user_states[key]

        
        await message.reply_text(
            f"✅ **تم إضافة الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}\n\n"
            f"🔄 **جاري إعادة تشغيل البوت لتفعيل الحساب المساعد...**",
            reply_markup=await assistant_main_keyboard()
        )

        
        import sys
        os.execv(sys.executable, ['python'] + sys.argv)

    except Exception as e:
        
        if "PASSWORD_HASH_INVALID" in str(e) or "Two-step verification" in str(e):
            user_states[user_id] = "waiting_password"
            await message.reply_text(
                f"🔐 **يرجى إرسال كلمة المرور (التحقق بخطوتين).**",
                reply_markup=await cancel_keyboard()
            )
        else:
            
            keys_to_delete = [user_id, f"{user_id}_assistant", f"{user_id}_phone", f"{user_id}_code_hash"]
            for key in keys_to_delete:
                if key in user_states:
                    del user_states[key]

            await message.reply_text(
                f"❌ **حدث خطأ أثناء تسجيل الدخول:**\n\n`{str(e)}`",
                reply_markup=await assistant_main_keyboard()
            )

async def handle_password_input(client: Client, message: Message):
    """معالجة إدخال كلمة المرور"""
    password = message.text.strip()
    user_id = message.from_user.id

    try:
        assistant = user_states.get(f"{user_id}_assistant")

        if not assistant:
            raise Exception("معلومات الجلسة مفقودة")

        
        await assistant.check_password(password)

        
        me = await assistant.get_me()

        
        session_string = await assistant.export_session_string()

        
        await add_assistant_account(
            me.id,
            session_string,
            me.first_name,
            me.username
        )

        
        await assistant.disconnect()

        
        keys_to_delete = [user_id, f"{user_id}_assistant", f"{user_id}_phone", f"{user_id}_code_hash"]
        for key in keys_to_delete:
            if key in user_states:
                del user_states[key]

        
        await message.reply_text(
            f"✅ **تم إضافة الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}\n\n"
            f"🔄 **جاري إعادة تشغيل البوت لتفعيل الحساب المساعد...**",
            reply_markup=await assistant_main_keyboard()
        )

        
        import sys
        os.execv(sys.executable, ['python'] + sys.argv)

    except Exception as e:
        
        keys_to_delete = [user_id, f"{user_id}_assistant", f"{user_id}_phone", f"{user_id}_code_hash"]
        for key in keys_to_delete:
            if key in user_states:
                del user_states[key]

        await message.reply_text(
            f"❌ **حدث خطأ أثناء تسجيل الدخول بكلمة المرور:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_first_name_input(client: Client, message: Message):
    """معالجة إدخال الاسم الأول"""
    new_name = message.text.strip()
    user_id = message.from_user.id

    
    if len(new_name) < 1 or len(new_name) > 64:
        await message.reply_text(
            "❌ **الاسم غير صحيح!**\n\n"
            "يجب أن يكون الاسم بين 1 و 64 حرف.",
            reply_markup=await cancel_keyboard()
        )
        return

    try:
        
        await client.assistant.update_profile(first_name=new_name)

        
        me = await client.assistant.get_me()
        await update_assistant_account(me.id, "first_name", new_name)

        del user_states[user_id]

        await message.reply_text(
            f"✅ **تم تحديث الاسم الأول بنجاح!**\n\n"
            f"**الاسم الجديد:** {new_name}",
            reply_markup=await assistant_main_keyboard()
        )

    except Exception as e:
        del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث الاسم الأول:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_last_name_input(client: Client, message: Message):
    """معالجة إدخال الاسم الثاني"""
    new_name = message.text.strip()
    user_id = message.from_user.id

    
    if new_name.lower() in ['حذف', 'delete', 'remove']:
        new_name = ""

    
    if len(new_name) > 64:
        await message.reply_text(
            "❌ **الاسم طويل جداً!**\n\n"
            "يجب أن يكون الاسم الثاني أقل من 64 حرف.",
            reply_markup=await cancel_keyboard()
        )
        return

    try:
        
        await client.assistant.update_profile(last_name=new_name if new_name else None)

        del user_states[user_id]

        result_text = "✅ **تم تحديث الاسم الثاني بنجاح!**\n\n"
        if new_name:
            result_text += f"**الاسم الثاني الجديد:** {new_name}"
        else:
            result_text += "**تم حذف الاسم الثاني.**"

        await message.reply_text(
            result_text,
            reply_markup=await assistant_main_keyboard()
        )

    except Exception as e:
        del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث الاسم الثاني:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_bio_input(client: Client, message: Message):
    """معالجة إدخال البايو"""
    new_bio = message.text.strip()
    user_id = message.from_user.id

    
    if new_bio.lower() in ['حذف', 'delete', 'remove']:
        new_bio = ""

    
    if len(new_bio) > 70:
        await message.reply_text(
            "❌ **البايو طويل جداً!**\n\n"
            "يجب أن يكون البايو أقل من 70 حرف.",
            reply_markup=await cancel_keyboard()
        )
        return

    try:
        
        await client.assistant.update_profile(bio=new_bio if new_bio else None)

        del user_states[user_id]

        result_text = "✅ **تم تحديث البايو بنجاح!**\n\n"
        if new_bio:
            result_text += f"**البايو الجديد:** {new_bio}"
        else:
            result_text += "**تم حذف البايو.**"

        await message.reply_text(
            result_text,
            reply_markup=await assistant_main_keyboard()
        )

    except Exception as e:
        del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث البايو:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_username_input(client: Client, message: Message):
    """معالجة إدخال اسم المستخدم"""
    new_username = message.text.strip()
    user_id = message.from_user.id

    
    if new_username.lower() in ['حذف', 'delete', 'remove']:
        new_username = ""

    
    if new_username:
        
        if new_username.startswith('@'):
            new_username = new_username[1:]

        
        if len(new_username) < 5 or len(new_username) > 32:
            await message.reply_text(
                "❌ **اسم المستخدم غير صحيح!**\n\n"
                "يجب أن يكون اسم المستخدم بين 5 و 32 حرف.",
                reply_markup=await cancel_keyboard()
            )
            return

        
        if not new_username.replace('_', '').isalnum():
            await message.reply_text(
                "❌ **اسم المستخدم يحتوي على أحرف غير مسموحة!**\n\n"
                "يمكن استخدام الأحرف والأرقام و _ فقط.",
                reply_markup=await cancel_keyboard()
            )
            return

    try:
        
        await client.assistant.set_username(new_username if new_username else None)

        
        me = await client.assistant.get_me()
        await update_assistant_account(me.id, "username", new_username if new_username else None)

        del user_states[user_id]

        result_text = "✅ **تم تحديث اسم المستخدم بنجاح!**\n\n"
        if new_username:
            result_text += f"**اسم المستخدم الجديد:** @{new_username}"
        else:
            result_text += "**تم حذف اسم المستخدم.**"

        await message.reply_text(
            result_text,
            reply_markup=await assistant_main_keyboard()
        )

    except Exception as e:
        del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث اسم المستخدم:**\n\n`{str(e)}`",
            reply_markup=await assistant_main_keyboard()
        )

async def handle_delete_confirmation(client: Client, message: Message):
    """معالجة تأكيد حذف الحساب المساعد"""
    confirmation = message.text.strip()
    user_id = message.from_user.id

    if confirmation == "✅ نعم، تأكيد":
        
        assistants = await get_all_assistant_accounts()

        if not assistants:
            del user_states[user_id]
            await message.reply_text(
                "⚠️ **لا يوجد حساب مساعد لحذفه.**",
                reply_markup=await assistant_main_keyboard()
            )
            return

        assistant_user_id = list(assistants.keys())[0]
        account = assistants[assistant_user_id]

        
        if await remove_assistant_account(assistant_user_id):
            del user_states[user_id]

            text = (
                f"✅ **تم حذف الحساب المساعد بنجاح!**\n\n"
                f"**الاسم:** {account.get('first_name', 'غير معروف')}\n"
                f"**المعرف:** `{assistant_user_id}`\n"
                f"**اسم المستخدم:** @{account.get('username', 'لا يوجد')}\n\n"
                f"🔄 **سيتم إعادة تشغيل البوت لتطبيق التغييرات...**"
            )

            await message.reply_text(
                text,
                reply_markup=await assistant_main_keyboard()
            )

            
            import sys
            os.execv(sys.executable, ['python'] + sys.argv)
        else:
            del user_states[user_id]
            await message.reply_text(
                "❌ **حدث خطأ أثناء حذف الحساب المساعد.**",
                reply_markup=await assistant_main_keyboard()
            )

    elif confirmation == "❌ لا، إلغاء":
        del user_states[user_id]
        await message.reply_text(
            "✅ **تم إلغاء عملية حذف الحساب المساعد.**",
            reply_markup=await assistant_main_keyboard()
        )

    else:
        await message.reply_text(
            "⚠️ **يرجى اختيار أحد الخيارات المتاحة.**",
            reply_markup=await confirm_keyboard()
        )


async def handle_photo_input(client: Client, message: Message):
    """معالجة رفع صورة جديدة"""
    user_id = message.from_user.id

    
    if not message.photo:
        await message.reply_text(
            "⚠️ **يرجى إرسال صورة وليس نص.**\n\n"
            "استخدم زر 'إلغاء العملية' للعودة.",
            reply_markup=await cancel_keyboard()
        )
        return

    
    if not hasattr(client, 'assistant') or not client.assistant:
        del user_states[user_id]
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_photo_keyboard()
        )
        return

    
    status_message = await message.reply_text(
        "🔄 **جاري تحميل الصورة...**\n\n"
        "⏳ **يرجى الانتظار...**"
    )

    temp_file_path = None
    try:
        
        temp_dir = ensure_temp_photos_dir()

        
        file_extension = "jpg"  
        temp_file_path = os.path.join(temp_dir, f"profile_photo_{user_id}_{message.id}.{file_extension}")

        
        await status_message.edit_text(
            "📥 **جاري تحميل الصورة من تليجرام...**\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        
        downloaded_file = await client.download_media(message.photo, file_name=temp_file_path)

        if not downloaded_file or not os.path.exists(downloaded_file):
            raise Exception("فشل في تحميل الصورة من تليجرام")

        
        await status_message.edit_text(
            "🖼 **جاري تحديث الصورة الشخصية للمساعد...**\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        
        await client.assistant.set_profile_photo(photo=downloaded_file)

        
        del user_states[user_id]

        
        await status_message.delete()
        await message.reply_text(
            "✅ **تم تحديث الصورة الشخصية بنجاح!**\n\n"
            "🖼 **تم رفع الصورة وتطبيقها على الحساب المساعد.**",
            reply_markup=await assistant_photo_keyboard()
        )

    except Exception as e:
        
        del user_states[user_id]

        
        try:
            await status_message.delete()
        except:
            pass

        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث الصورة:**\n\n"
            f"`{str(e)}`\n\n"
            f"💡 **نصائح لحل المشكلة:**\n"
            f"• تأكد من أن الصورة بصيغة صحيحة (JPG, PNG)\n"
            f"• تأكد من أن حجم الصورة مناسب (أقل من 10 ميجابايت)\n"
            f"• تأكد من أن الحساب المساعد متصل بشكل صحيح",
            reply_markup=await assistant_photo_keyboard()
        )

    finally:
        
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except Exception as cleanup_error:
                
                print(f"تحذير: فشل في حذف الملف المؤقت {temp_file_path}: {cleanup_error}")

async def handle_photo_url_input(client: Client, message: Message):
    """معالجة رابط الصورة"""
    photo_url = message.text.strip()
    user_id = message.from_user.id

    
    if not photo_url.startswith(('http://', 'https://')):
        await message.reply_text(
            "❌ **رابط غير صحيح!**\n\n"
            "يجب أن يبدأ الرابط بـ http:// أو https://",
            reply_markup=await cancel_keyboard()
        )
        return

    
    if not hasattr(client, 'assistant') or not client.assistant:
        del user_states[user_id]
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_photo_keyboard()
        )
        return

    
    status_message = await message.reply_text(
        "🔄 **جاري تحميل الصورة من الرابط...**\n\n"
        "⏳ **يرجى الانتظار...**"
    )

    
    if not check_url_download_dependencies():
        del user_states[user_id]
        try:
            await status_message.delete()
        except:
            pass

        await message.reply_text(
            "❌ **خطأ في النظام: مكتبات التحميل غير متوفرة**\n\n"
            "يرجى التواصل مع المطور لتثبيت المكتبات المطلوبة:\n"
            "• aiohttp\n"
            "• aiofiles",
            reply_markup=await assistant_photo_keyboard()
        )
        return

    temp_file_path = None
    try:
        import aiohttp
        import aiofiles

        
        temp_dir = ensure_temp_photos_dir()

        
        file_extension = "jpg"  
        if photo_url.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
            file_extension = photo_url.split('.')[-1].lower()

        
        temp_file_path = os.path.join(temp_dir, f"profile_photo_url_{user_id}_{message.id}.{file_extension}")

        
        await status_message.edit_text(
            "📥 **جاري تحميل الصورة من الرابط...**\n\n"
            f"🔗 **الرابط:** {photo_url[:50]}{'...' if len(photo_url) > 50 else ''}\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        async with aiohttp.ClientSession() as session:
            async with session.get(photo_url) as response:
                if response.status != 200:
                    raise Exception(f"فشل في تحميل الصورة: HTTP {response.status}")

                
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    raise Exception("الرابط لا يشير إلى صورة صحيحة")

                
                async with aiofiles.open(temp_file_path, 'wb') as file:
                    async for chunk in response.content.iter_chunked(8192):
                        await file.write(chunk)

        
        if not os.path.exists(temp_file_path):
            raise Exception("فشل في حفظ الصورة المحملة")

        
        await status_message.edit_text(
            "🖼 **جاري تحديث الصورة الشخصية للمساعد...**\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        
        await client.assistant.set_profile_photo(photo=temp_file_path)

        
        del user_states[user_id]

        
        await status_message.delete()
        await message.reply_text(
            f"✅ **تم تحديث الصورة الشخصية بنجاح!**\n\n"
            f"🔗 **الرابط:** {photo_url}\n\n"
            f"🖼 **تم تحميل الصورة وتطبيقها على الحساب المساعد.**",
            reply_markup=await assistant_photo_keyboard()
        )

    except Exception as e:
        
        del user_states[user_id]

        
        try:
            await status_message.delete()
        except:
            pass

        await message.reply_text(
            f"❌ **حدث خطأ أثناء تحديث الصورة:**\n\n"
            f"`{str(e)}`\n\n"
            f"💡 **نصائح لحل المشكلة:**\n"
            f"• تأكد من أن الرابط صحيح ويشير إلى صورة\n"
            f"• تأكد من أن الصورة متاحة للتحميل\n"
            f"• جرب رابط صورة مختلف\n"
            f"• تأكد من أن الحساب المساعد متصل بشكل صحيح",
            reply_markup=await assistant_photo_keyboard()
        )

    finally:
        
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except Exception as cleanup_error:
                
                print(f"تحذير: فشل في حذف الملف المؤقت {temp_file_path}: {cleanup_error}")

async def handle_invite_link_input(client: Client, message: Message):
    """معالجة رابط الدعوة"""
    invite_link = message.text.strip()
    user_id = message.from_user.id

    
    if not invite_link.startswith(('https://t.me/', 't.me/')):
        await message.reply_text(
            "❌ **رابط غير صحيح!**\n\n"
            "يجب أن يكون الرابط من تليجرام.",
            reply_markup=await cancel_keyboard()
        )
        return

    try:
        
        await client.assistant.join_chat(invite_link)

        
        me = await client.assistant.get_me()

        del user_states[user_id]

        await message.reply_text(
            f"✅ **تم انضمام الحساب المساعد بنجاح!**\n\n"
            f"👤 **الاسم:** {me.first_name}\n"
            f"🆔 **المعرف:** `{me.id}`\n"
            f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}\n"
            f"🔗 **الرابط:** {invite_link}",
            reply_markup=await assistant_groups_keyboard()
        )

    except Exception as e:
        del user_states[user_id]
        await message.reply_text(
            f"❌ **حدث خطأ أثناء دعوة الحساب المساعد:**\n\n`{str(e)}`",
            reply_markup=await assistant_groups_keyboard()
        )




async def delete_profile_photo(client: Client, message: Message):
    """حذف الصورة الشخصية"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_photo_keyboard()
        )
        return

    try:
        
        photos = []
        async for photo in client.assistant.get_chat_photos("me"):
            photos.append(photo)

        if not photos:
            await message.reply_text(
                "⚠️ **لا توجد صور شخصية لحذفها.**",
                reply_markup=await assistant_photo_keyboard()
            )
            return

        
        await client.assistant.delete_profile_photos(photos[0].file_id)

        await message.reply_text(
            "✅ **تم حذف الصورة الشخصية بنجاح!**",
            reply_markup=await assistant_photo_keyboard()
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء حذف الصورة:**\n\n`{str(e)}`",
            reply_markup=await assistant_photo_keyboard()
        )

async def leave_all_groups(client: Client, message: Message):
    """خروج من جميع المجموعات"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_groups_keyboard()
        )
        return

    try:
        status_message = await message.reply_text(
            "🔄 **جاري الخروج من جميع المجموعات...**\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        left_count = 0
        error_count = 0

        
        async for dialog in client.assistant.get_dialogs():
            if dialog.chat.type in ["group", "supergroup", "channel"]:
                try:
                    await client.assistant.leave_chat(dialog.chat.id)
                    left_count += 1
                except:
                    error_count += 1

        
        me = await client.assistant.get_me()

        result_text = f"✅ **تم الخروج من المجموعات بنجاح!**\n\n"
        result_text += f"👤 **الاسم:** {me.first_name}\n"
        result_text += f"🆔 **المعرف:** `{me.id}`\n"
        result_text += f"📝 **اسم المستخدم:** @{me.username if me.username else 'لا يوجد'}\n\n"
        result_text += f"✅ **تم الخروج من:** {left_count} مجموعة\n"
        if error_count > 0:
            result_text += f"❌ **فشل الخروج من:** {error_count} مجموعة"

        await status_message.edit_text(
            result_text,
            reply_markup=await assistant_groups_keyboard()
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء الخروج من المجموعات:**\n\n`{str(e)}`",
            reply_markup=await assistant_groups_keyboard()
        )

async def list_joined_groups(client: Client, message: Message):
    """عرض المجموعات المنضم إليها"""
    
    if not hasattr(client, 'assistant') or not client.assistant:
        await message.reply_text(
            "⚠️ **لا يوجد حساب مساعد نشط.**",
            reply_markup=await assistant_groups_keyboard()
        )
        return

    try:
        status_message = await message.reply_text(
            "🔄 **جاري جمع معلومات المجموعات...**\n\n"
            "⏳ **يرجى الانتظار...**"
        )

        groups = []
        channels = []

        
        async for dialog in client.assistant.get_dialogs():
            if dialog.chat.type == "group" or dialog.chat.type == "supergroup":
                groups.append(f"• {dialog.chat.title}")
            elif dialog.chat.type == "channel":
                channels.append(f"• {dialog.chat.title}")

        
        result_text = "📋 **المجموعات والقنوات المنضم إليها:**\n\n"

        if groups:
            result_text += f"👥 **المجموعات ({len(groups)}):**\n"
            result_text += "\n".join(groups[:10])  
            if len(groups) > 10:
                result_text += f"\n... و {len(groups) - 10} مجموعة أخرى"
            result_text += "\n\n"

        if channels:
            result_text += f"📢 **القنوات ({len(channels)}):**\n"
            result_text += "\n".join(channels[:10])  
            if len(channels) > 10:
                result_text += f"\n... و {len(channels) - 10} قناة أخرى"
            result_text += "\n\n"

        if not groups and not channels:
            result_text += "⚠️ **لا يوجد مجموعات أو قنوات منضم إليها.**"

        result_text += f"📊 **المجموع:** {len(groups) + len(channels)}"

        await status_message.edit_text(
            result_text,
            reply_markup=await assistant_groups_keyboard()
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **حدث خطأ أثناء جمع معلومات المجموعات:**\n\n`{str(e)}`",
            reply_markup=await assistant_groups_keyboard()
        )


async def show_bot_statistics(client: Client, message: Message):
    """عرض إحصائيات البوت"""
    from database.users_db import count_users, count_groups, get_all_banned_users, get_all_banned_groups
    from database.redis_db import redis_keys

    
    users_count = await count_users()
    groups_count = await count_groups()
    banned_users = await get_all_banned_users()
    banned_groups = await get_all_banned_groups()

    
    muted_keys = await redis_keys("muted_user:*")
    muted_count = len(muted_keys) if muted_keys else 0

    
    from plugins.dev.broadcast import get_all_channels
    channels = await get_all_channels()
    channels_count = len(channels) if channels else 0

    stats_text = (
        f"📊 **إحصائيات البوت**\n\n"
        f"👥 **المستخدمين:** {users_count}\n"
        f"🏘 **المجموعات:** {groups_count}\n"
        f"📢 **القنوات:** {channels_count}\n\n"
        f"🚫 **المحظورين:**\n"
        f"   • المستخدمين: {len(banned_users)}\n"
        f"   • المجموعات: {len(banned_groups)}\n\n"
        f"🔇 **المكتومين:** {muted_count}\n\n"
        f"📊 **إجمالي الدردشات:** {users_count + groups_count + channels_count}"
    )

    await message.reply_text(
        stats_text,
        reply_markup=await dev_main_keyboard()
    )

async def show_broadcast_menu(client: Client, message: Message):
    """عرض قائمة الإذاعات"""
    text = (
        f"📢 **إدارة الإذاعات**\n\n"
        f"🎛 **استخدم الأزرار أدناه لإدارة الإذاعات:**\n\n"
        f"📢 **إذاعة عامة** - إرسال لجميع المستخدمين والمجموعات والقنوات\n"
        f"👤 **إذاعة للمستخدمين** - إرسال للمستخدمين فقط\n"
        f"🏘 **إذاعة للمجموعات** - إرسال للمجموعات فقط\n"
        f"📺 **إذاعة للقنوات** - إرسال للقنوات فقط\n"
        f"📊 **إحصائيات الإذاعة** - عرض تقارير الإذاعات\n"
        f"⚙️ **إعدادات الإذاعة** - إدارة القنوات والإعدادات"
    )

    await message.reply_text(
        text,
        reply_markup=await broadcast_main_keyboard()
    )

async def show_bot_settings_menu(client: Client, message: Message):
    """عرض قائمة إعدادات البوت"""
    text = (
        f"⚙️ **إعدادات البوت**\n\n"
        f"🎛 **استخدم الأزرار أدناه لتخصيص إعدادات البوت:**\n\n"
        f"🏷 **إعدادات الهوية** - اسم البوت والسورس والمطور\n"
        f"🔗 **إعدادات الروابط** - قناة ومجموعة البوت\n"
        f"🔒 **إعدادات الأمان** - الاشتراك الإجباري والحماية\n"
        f"🎵 **إعدادات التشغيل** - إعدادات تشغيل الموسيقى\n"
        f"📢 **إعدادات الإذاعة** - إدارة القنوات والإذاعات\n"
        f"💬 **إعدادات التواصل** - إعدادات التواصل مع المستخدمين"
    )

    await message.reply_text(
        text,
        reply_markup=await bot_settings_main_keyboard()
    )

async def show_ban_management_menu(client: Client, message: Message):
    """عرض قائمة إدارة الحظر"""
    text = (
        f"🚫 **إدارة الحظر والكتم**\n\n"
        f"🎛 **استخدم الأزرار أدناه لإدارة المستخدمين والمجموعات:**\n\n"
        f"🚫 **حظر مستخدم** - حظر مستخدم من استخدام البوت\n"
        f"✅ **إلغاء حظر مستخدم** - إلغاء حظر مستخدم\n"
        f"🔇 **كتم مستخدم** - كتم مستخدم مؤقتاً أو دائماً\n"
        f"🔊 **إلغاء كتم مستخدم** - إلغاء كتم مستخدم\n"
        f"🚫 **حظر مجموعة** - حظر مجموعة من استخدام البوت\n"
        f"✅ **إلغاء حظر مجموعة** - إلغاء حظر مجموعة\n"
        f"📋 **قائمة المحظورين** - عرض المستخدمين والمجموعات المحظورة\n"
        f"📋 **قائمة المكتومين** - عرض المستخدمين المكتومين"
    )

    await message.reply_text(
        text,
        reply_markup=await ban_management_keyboard()
    )

async def show_rank_management_menu(client: Client, message: Message):
    """عرض قائمة إدارة الرتب"""
    text = (
        f"👥 **إدارة الرتب والصلاحيات**\n\n"
        f"🎛 **استخدم الأزرار أدناه لإدارة رتب المستخدمين:**\n\n"
        f"👨‍💻 **المطورين** - إضافة وحذف المطورين\n"
        f"👮‍♂️ **المشرفين** - إضافة وحذف المشرفين\n"
        f"⭐ **VIP** - إضافة وحذف مستخدمي VIP\n"
        f"📋 **القوائم** - عرض قوائم المطورين والمشرفين و VIP\n\n"
        f"💡 **ملاحظة:** المطورين لديهم صلاحيات كاملة، المشرفين لديهم صلاحيات محدودة، و VIP لديهم مميزات خاصة."
    )

    await message.reply_text(
        text,
        reply_markup=await rank_management_keyboard()
    )

async def show_log_settings_menu(client: Client, message: Message):
    """عرض قائمة إعدادات السجل"""
    text = (
        f"📝 **إعدادات سجل التشغيل**\n\n"
        f"🎛 **إدارة سجلات البوت والأنشطة:**\n\n"
        f"🔄 **تفعيل/تعطيل السجل** - تشغيل أو إيقاف تسجيل الأنشطة\n"
        f"📍 **تعيين قناة السجل** - تحديد قناة لإرسال السجلات\n"
        f"📊 **عرض السجلات** - مراجعة السجلات الحديثة\n"
        f"🗑 **تنظيف السجلات** - حذف السجلات القديمة\n\n"
        f"💡 **ملاحظة:** السجلات تساعد في مراقبة أنشطة البوت وتشخيص المشاكل."
    )

    await message.reply_text(
        text,
        reply_markup=await dev_main_keyboard()
    )

async def show_developer_tools_menu(client: Client, message: Message):
    """عرض قائمة أدوات المطور"""
    text = (
        f"🔧 **أدوات المطور المتقدمة**\n\n"
        f"🎛 **أدوات صيانة وإدارة النظام:**\n\n"
        f"🔄 **إعادة تشغيل البوت** - إعادة تشغيل البوت بأمان\n"
        f"📊 **معلومات النظام** - عرض معلومات الخادم والذاكرة\n"
        f"🗂 **تنظيف قاعدة البيانات** - حذف البيانات غير المستخدمة\n"
        f"💾 **نسخ احتياطي** - إنشاء نسخة احتياطية من البيانات\n"
        f"🧪 **اختبار الاتصالات** - فحص اتصال Redis والخدمات\n"
        f"📈 **تقرير الأداء** - عرض إحصائيات الأداء والاستخدام\n\n"
        f"⚠️ **تحذير:** استخدم هذه الأدوات بحذر!"
    )

    await message.reply_text(
        text,
        reply_markup=await developer_tools_keyboard()
    )


async def show_identity_settings(client: Client, message: Message):
    """عرض إعدادات الهوية"""
    from database.settings_db import get_bot_name, get_source_name, get_developer_name

    
    bot_name = await get_bot_name()
    source_name = await get_source_name()
    developer_name = await get_developer_name()

    text = (
        f"🏷 **إعدادات هوية البوت**\n\n"
        f"**الإعدادات الحالية:**\n"
        f"🤖 **اسم البوت:** {bot_name}\n"
        f"📦 **اسم السورس:** {source_name}\n"
        f"👨‍💻 **اسم المطور:** {developer_name}\n\n"
        f"🎛 **استخدم الأزرار أدناه لتعديل الإعدادات:**"
    )

    await message.reply_text(
        text,
        reply_markup=await identity_settings_keyboard()
    )

async def show_links_settings(client: Client, message: Message):
    """عرض إعدادات الروابط"""
    from database.settings_db import get_channel, get_group

    
    channel = await get_channel()
    group = await get_group()

    text = (
        f"🔗 **إعدادات روابط البوت**\n\n"
        f"**الروابط الحالية:**\n"
        f"📢 **قناة البوت:** {channel}\n"
        f"👥 **مجموعة البوت:** {group}\n\n"
        f"🎛 **استخدم الأزرار أدناه لتعديل الروابط:**"
    )

    await message.reply_text(
        text,
        reply_markup=await links_settings_keyboard()
    )

async def show_security_settings(client: Client, message: Message):
    """عرض إعدادات الأمان"""
    from database.settings_db import (
        is_force_sub_enabled, is_advanced_perms_enabled, is_communication_enabled
    )

    
    force_sub = await is_force_sub_enabled()
    advanced_perms = await is_advanced_perms_enabled()
    communication = await is_communication_enabled()

    force_sub_status = "🟢 مفعل" if force_sub else "🔴 معطل"
    advanced_perms_status = "🟢 مفعل" if advanced_perms else "🔴 معطل"
    communication_status = "🟢 مفعل" if communication else "🔴 معطل"

    text = (
        f"🔒 **إعدادات أمان البوت**\n\n"
        f"**الحالة الحالية:**\n"
        f"🔐 **الاشتراك الإجباري:** {force_sub_status}\n"
        f"⚡ **صلاحيات التشغيل المتقدمة:** {advanced_perms_status}\n"
        f"💬 **التواصل:** {communication_status}\n\n"
        f"🎛 **استخدم الأزرار أدناه لتعديل الإعدادات:**"
    )

    await message.reply_text(
        text,
        reply_markup=await security_settings_keyboard()
    )

async def show_music_settings(client: Client, message: Message):
    """عرض إعدادات التشغيل"""
    text = (
        f"🎵 **إعدادات تشغيل الموسيقى**\n\n"
        f"🎛 **إعدادات التشغيل والجودة:**\n\n"
        f"🔊 **جودة الصوت** - تحديد جودة تشغيل الموسيقى\n"
        f"📻 **مصادر التشغيل** - إدارة مصادر الموسيقى المدعومة\n"
        f"⏱ **مدة التشغيل** - تحديد الحد الأقصى لمدة التشغيل\n"
        f"🔄 **التشغيل التلقائي** - إعدادات التشغيل التلقائي\n"
        f"📝 **سجل التشغيل** - تسجيل أنشطة التشغيل\n\n"
        f"💡 **ملاحظة:** هذه الإعدادات تؤثر على جودة وأداء تشغيل الموسيقى."
    )

    await message.reply_text(
        text,
        reply_markup=await bot_settings_main_keyboard()
    )

async def show_broadcast_settings(client: Client, message: Message):
    """عرض إعدادات الإذاعة"""
    from plugins.dev.broadcast import get_all_channels

    
    channels = await get_all_channels()
    channels_count = len(channels) if channels else 0

    text = (
        f"📢 **إعدادات الإذاعة**\n\n"
        f"**الحالة الحالية:**\n"
        f"📺 **القنوات المضافة:** {channels_count}\n\n"
        f"🎛 **استخدم الأزرار أدناه لإدارة الإذاعات:**\n\n"
        f"📢 **إضافة قناة** - إضافة قناة جديدة للإذاعة\n"
        f"🗑 **حذف قناة** - إزالة قناة من الإذاعة\n"
        f"📋 **قائمة القنوات** - عرض جميع القنوات المضافة\n"
        f"🧪 **اختبار الإذاعة** - إرسال رسالة تجريبية\n"
        f"⚙️ **إعدادات متقدمة** - إعدادات الإذاعة المتقدمة"
    )

    await message.reply_text(
        text,
        reply_markup=await broadcast_settings_keyboard()
    )

async def show_communication_settings(client: Client, message: Message):
    """عرض إعدادات التواصل"""
    from database.settings_db import is_communication_enabled

    
    communication = await is_communication_enabled()
    communication_status = "🟢 مفعل" if communication else "🔴 معطل"

    text = (
        f"💬 **إعدادات التواصل**\n\n"
        f"**الحالة الحالية:**\n"
        f"📞 **التواصل:** {communication_status}\n\n"
        f"🎛 **إعدادات التواصل مع المستخدمين:**\n\n"
        f"📨 **الرسائل الخاصة** - السماح بالرسائل الخاصة للمطور\n"
        f"📢 **الإشعارات** - إعدادات الإشعارات والتنبيهات\n"
        f"🤖 **الردود التلقائية** - إعداد ردود تلقائية\n"
        f"📝 **رسائل الترحيب** - تخصيص رسائل الترحيب\n"
        f"🔔 **تنبيهات المطور** - إشعارات للمطور عن الأنشطة\n\n"
        f"💡 **ملاحظة:** هذه الإعدادات تحكم في طريقة تفاعل البوت مع المستخدمين."
    )

    await message.reply_text(
        text,
        reply_markup=await bot_settings_main_keyboard()
    )
