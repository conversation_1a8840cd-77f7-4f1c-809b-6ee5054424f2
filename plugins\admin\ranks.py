from pyrogram import Client, filters
from pyrogram.types import Message
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.users_db import (
    add_vip, remove_vip, is_vip,
    add_developer, remove_developer, is_developer,
    add_admin, remove_admin, is_admin
)


async def extract_user_enhanced(client: Client, message: Message):
    """استخراج معرف المستخدم مع دعم طرق متعددة"""
    user_id = None
    user_info = None

    # الرد على رسالة
    if message.reply_to_message and message.reply_to_message.from_user:
        user_id = message.reply_to_message.from_user.id
        user_info = {
            'id': user_id,
            'first_name': message.reply_to_message.from_user.first_name,
            'username': message.reply_to_message.from_user.username
        }
        return user_id, user_info

    # من النص
    if len(message.command) > 1:
        text = message.command[1]

        # اسم المستخدم
        if text.startswith("@"):
            username = text[1:]
            try:
                user = await client.get_users(username)
                user_id = user.id
                user_info = {
                    'id': user_id,
                    'first_name': user.first_name,
                    'username': user.username
                }
                return user_id, user_info
            except:
                return None, None

        # معرف رقمي
        if text.isdigit():
            user_id = int(text)
            try:
                user = await client.get_users(user_id)
                user_info = {
                    'id': user_id,
                    'first_name': user.first_name,
                    'username': user.username
                }
                return user_id, user_info
            except:
                return user_id, {'id': user_id, 'first_name': 'مستخدم غير معروف', 'username': None}

    return None, None


@Client.on_message(command_handler(["رفع vip", "رفع مميز"]) & dev)
@dev_only
async def promote_vip_command(client: Client, message: Message):
    """رفع مستخدم إلى رتبة VIP"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد رفعه إلى VIP.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`رفع vip @username`\n"
            "`رفع مميز 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `رفع vip`"
        )
        return

    if await is_vip(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} مميز بالفعل.**"
        )
        return

    await add_vip(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"⭐ **تم رفع المستخدم {user_display} إلى رتبة VIP بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["تنزيل vip", "تنزيل مميز"]) & dev)
@dev_only
async def demote_vip_command(client: Client, message: Message):
    """تنزيل مستخدم من رتبة VIP"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد تنزيله من VIP.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`تنزيل vip @username`\n"
            "`تنزيل مميز 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `تنزيل vip`"
        )
        return

    if not await is_vip(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} ليس مميزًا.**"
        )
        return

    await remove_vip(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"⭐ **تم تنزيل المستخدم {user_display} من رتبة VIP بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["رفع مطور"]) & dev)
@dev_only
async def promote_developer_command(client: Client, message: Message):
    """رفع مستخدم إلى رتبة مطور"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد رفعه إلى مطور.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`رفع مطور @username`\n"
            "`رفع مطور 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `رفع مطور`"
        )
        return

    if await is_developer(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} مطور بالفعل.**"
        )
        return

    await add_developer(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"👨‍💻 **تم رفع المستخدم {user_display} إلى رتبة مطور بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["تنزيل مطور"]) & dev)
@dev_only
async def demote_developer_command(client: Client, message: Message):
    """تنزيل مستخدم من رتبة مطور"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد تنزيله من مطور.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`تنزيل مطور @username`\n"
            "`تنزيل مطور 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `تنزيل مطور`"
        )
        return

    if not await is_developer(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} ليس مطورًا.**"
        )
        return

    await remove_developer(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"👨‍💻 **تم تنزيل المستخدم {user_display} من رتبة مطور بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["رفع ادمن", "رفع أدمن"]) & dev)
@dev_only
async def promote_admin_command(client: Client, message: Message):
    """رفع مستخدم إلى رتبة أدمن"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد رفعه إلى أدمن.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`رفع ادمن @username`\n"
            "`رفع أدمن 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `رفع ادمن`"
        )
        return

    if await is_admin(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} أدمن بالفعل.**"
        )
        return

    await add_admin(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"👑 **تم رفع المستخدم {user_display} إلى رتبة أدمن بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["تنزيل ادمن", "تنزيل أدمن"]) & dev)
@dev_only
async def demote_admin_command(client: Client, message: Message):
    """تنزيل مستخدم من رتبة أدمن"""
    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد تنزيله من أدمن.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`تنزيل ادمن @username`\n"
            "`تنزيل أدمن 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `تنزيل ادمن`"
        )
        return

    if not await is_admin(user_id):
        user_display = user_info['first_name'] if user_info else str(user_id)
        await message.reply_text(
            f"⚠️ **المستخدم {user_display} ليس أدمنًا.**"
        )
        return

    await remove_admin(user_id)

    user_display = f"{user_info['first_name']}" if user_info else str(user_id)
    if user_info and user_info.get('username'):
        user_display += f" (@{user_info['username']})"

    await message.reply_text(
        f"👑 **تم تنزيل المستخدم {user_display} من رتبة أدمن بنجاح.**\n"
        f"🆔 **المعرف:** `{user_id}`"
    )


@Client.on_message(command_handler(["رفع مشرف"]) & dev)
@dev_only
async def promote_group_admin_command(client: Client, message: Message):
    """رفع مستخدم إلى مشرف في المجموعة"""
    if not message.chat.type in ["group", "supergroup"]:
        await message.reply_text("⚠️ **هذا الأمر يعمل في المجموعات فقط.**")
        return

    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد رفعه إلى مشرف.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`رفع مشرف @username`\n"
            "`رفع مشرف 123456789 لقب مخصص`\n"
            "أو الرد على رسالة المستخدم مع كتابة `رفع مشرف`"
        )
        return

    # استخراج اللقب المخصص إن وجد
    custom_title = None
    if len(message.command) > 2:
        custom_title = " ".join(message.command[2:])

    try:
        # رفع المستخدم إلى مشرف
        await client.promote_chat_member(
            chat_id=message.chat.id,
            user_id=user_id,
            privileges={
                "can_manage_chat": True,
                "can_delete_messages": True,
                "can_manage_video_chats": True,
                "can_restrict_members": True,
                "can_promote_members": False,
                "can_change_info": True,
                "can_invite_users": True,
                "can_pin_messages": True
            }
        )

        # تعيين اللقب المخصص إن وجد
        if custom_title:
            await client.set_administrator_title(
                chat_id=message.chat.id,
                user_id=user_id,
                title=custom_title
            )

        user_display = f"{user_info['first_name']}" if user_info else str(user_id)
        if user_info and user_info.get('username'):
            user_display += f" (@{user_info['username']})"

        success_text = f"👮‍♂️ **تم رفع المستخدم {user_display} إلى مشرف بنجاح.**\n"
        success_text += f"🆔 **المعرف:** `{user_id}`"
        if custom_title:
            success_text += f"\n🏷️ **اللقب:** {custom_title}"

        await message.reply_text(success_text)

    except Exception as e:
        await message.reply_text(
            f"❌ **فشل في رفع المستخدم إلى مشرف.**\n"
            f"**السبب:** {str(e)}"
        )


@Client.on_message(command_handler(["تنزيل مشرف"]) & dev)
@dev_only
async def demote_group_admin_command(client: Client, message: Message):
    """تنزيل مستخدم من مشرف في المجموعة"""
    if not message.chat.type in ["group", "supergroup"]:
        await message.reply_text("⚠️ **هذا الأمر يعمل في المجموعات فقط.**")
        return

    user_id, user_info = await extract_user_enhanced(client, message)

    if not user_id:
        await message.reply_text(
            "⚠️ **يرجى تحديد المستخدم المراد تنزيله من مشرف.**\n\n"
            "يمكنك تحديد المستخدم بإحدى الطرق التالية:\n"
            "• الرد على رسالة المستخدم\n"
            "• كتابة معرف المستخدم الرقمي\n"
            "• كتابة اسم مستخدم المستخدم (@username)\n\n"
            "**أمثلة:**\n"
            "`تنزيل مشرف @username`\n"
            "`تنزيل مشرف 123456789`\n"
            "أو الرد على رسالة المستخدم مع كتابة `تنزيل مشرف`"
        )
        return

    try:
        # تنزيل المستخدم من مشرف
        await client.promote_chat_member(
            chat_id=message.chat.id,
            user_id=user_id,
            privileges={
                "can_manage_chat": False,
                "can_delete_messages": False,
                "can_manage_video_chats": False,
                "can_restrict_members": False,
                "can_promote_members": False,
                "can_change_info": False,
                "can_invite_users": False,
                "can_pin_messages": False
            }
        )

        user_display = f"{user_info['first_name']}" if user_info else str(user_id)
        if user_info and user_info.get('username'):
            user_display += f" (@{user_info['username']})"

        await message.reply_text(
            f"👮‍♂️ **تم تنزيل المستخدم {user_display} من مشرف بنجاح.**\n"
            f"🆔 **المعرف:** `{user_id}`"
        )

    except Exception as e:
        await message.reply_text(
            f"❌ **فشل في تنزيل المستخدم من مشرف.**\n"
            f"**السبب:** {str(e)}"
        )
