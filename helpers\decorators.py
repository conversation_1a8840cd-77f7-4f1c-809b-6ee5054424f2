import functools
from pyrogram import Client
from pyrogram.types import Message
from config import OWNER_ID
from database.users_db import (
    is_developer, is_admin, is_vip, is_user_banned, is_group_banned
)
from database.settings_db import is_force_sub_enabled, get_channel


def owner_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        if message.from_user.id == OWNER_ID:
            return await func(client, message)
        await message.reply_text("⚠️ هذا الأمر متاح فقط لمطور السورس.")
    return decorator


def dev_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        user_id = message.from_user.id
        if user_id == OWNER_ID or await is_developer(user_id):
            return await func(client, message)
        await message.reply_text("⚠️ هذا الأمر متاح فقط للمطورين.")
    return decorator


def admin_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        user_id = message.from_user.id
        if (user_id == OWNER_ID or 
            await is_developer(user_id) or 
            await is_admin(user_id)):
            return await func(client, message)
        
        
        if message.chat.type in ["group", "supergroup"]:
            member = await client.get_chat_member(
                message.chat.id, user_id
            )
            if member.status in ["creator", "administrator"]:
                return await func(client, message)
        
        await message.reply_text("⚠️ هذا الأمر متاح فقط للمشرفين.")
    return decorator


def vip_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        user_id = message.from_user.id
        if (user_id == OWNER_ID or 
            await is_developer(user_id) or 
            await is_admin(user_id) or 
            await is_vip(user_id)):
            return await func(client, message)
        await message.reply_text("⚠️ هذا الأمر متاح فقط للمستخدمين المميزين.")
    return decorator


def check_banned(func):
    @functools.wraps(func)
    async def decorator(client, message):
        user_id = message.from_user.id
        
        
        if await is_user_banned(user_id):
            await message.reply_text("⛔ أنت محظور من استخدام البوت.")
            return
        
        
        if message.chat.type in ["group", "supergroup"]:
            if await is_group_banned(message.chat.id):
                await message.reply_text("⛔ هذه المجموعة محظورة من استخدام البوت.")
                return
        
        return await func(client, message)
    return decorator


def check_force_sub(func):
    @functools.wraps(func)
    async def decorator(client, message):
        user_id = message.from_user.id
        
        
        if (user_id == OWNER_ID or 
            await is_developer(user_id) or 
            await is_admin(user_id)):
            return await func(client, message)
        
        
        if await is_force_sub_enabled():
            channel = await get_channel()
            if not channel:
                return await func(client, message)
            
            try:
                
                member = await client.get_chat_member(channel, user_id)
                if member.status in ["left", "kicked"]:
                    await message.reply_text(
                        f"⚠️ يجب عليك الاشتراك في القناة أولاً للاستمرار.\n\n{channel}"
                    )
                    return
            except Exception:
                await message.reply_text(
                    f"⚠️ يجب عليك الاشتراك في القناة أولاً للاستمرار.\n\n{channel}"
                )
                return
        
        return await func(client, message)
    return decorator


def group_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        if message.chat.type in ["group", "supergroup"]:
            return await func(client, message)
        await message.reply_text("⚠️ هذا الأمر متاح فقط في المجموعات.")
    return decorator


def private_only(func):
    @functools.wraps(func)
    async def decorator(client, message):
        if message.chat.type == "private":
            return await func(client, message)
        await message.reply_text("⚠️ هذا الأمر متاح فقط في الخاص.")
    return decorator
