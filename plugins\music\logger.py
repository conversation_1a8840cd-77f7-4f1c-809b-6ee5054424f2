"""
وحدة التسجيل والمعالجة للأخطاء في نظام الموسيقى
تدير تسجيل الأحداث ومعالجة الأخطاء بشكل شامل
"""

import logging
import traceback
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from pyrogram import Client
from pyrogram.types import Message
from database.redis_db import get_redis


# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/music_bot.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

music_logger = logging.getLogger("MusicBot")


class MusicErrorHandler:
    """معالج الأخطاء الخاص بنظام الموسيقى"""
    
    def __init__(self):
        self.error_count = 0
        self.last_errors = []
        
    async def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """تسجيل خطأ مع السياق"""
        try:
            self.error_count += 1
            error_info = {
                "timestamp": datetime.now().isoformat(),
                "error_type": type(error).__name__,
                "error_message": str(error),
                "traceback": traceback.format_exc(),
                "context": context or {}
            }
            
            # حفظ في Redis
            redis = await get_redis()
            await redis.lpush("music_errors", str(error_info))
            await redis.ltrim("music_errors", 0, 99)  # الاحتفاظ بآخر 100 خطأ
            
            # تسجيل في الملف
            music_logger.error(
                f"خطأ في الموسيقى: {error_info['error_type']} - {error_info['error_message']}\n"
                f"السياق: {error_info['context']}\n"
                f"التفاصيل: {error_info['traceback']}"
            )
            
            # إضافة للقائمة المحلية
            self.last_errors.append(error_info)
            if len(self.last_errors) > 10:
                self.last_errors.pop(0)
                
        except Exception as e:
            music_logger.critical(f"فشل في تسجيل الخطأ: {e}")
    
    async def log_action(self, action: str, chat_id: int, user_id: int, details: Dict[str, Any] = None):
        """تسجيل إجراء موسيقى"""
        try:
            action_info = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "chat_id": chat_id,
                "user_id": user_id,
                "details": details or {}
            }
            
            # حفظ في Redis
            redis = await get_redis()
            await redis.lpush("music_actions", str(action_info))
            await redis.ltrim("music_actions", 0, 499)  # الاحتفاظ بآخر 500 إجراء
            
            # تسجيل في الملف
            music_logger.info(
                f"إجراء موسيقى: {action} - المجموعة: {chat_id} - المستخدم: {user_id} - التفاصيل: {details}"
            )
            
        except Exception as e:
            music_logger.error(f"فشل في تسجيل الإجراء: {e}")
    
    async def get_error_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        try:
            redis = await get_redis()
            errors = await redis.lrange("music_errors", 0, -1)
            
            error_types = {}
            recent_errors = []
            
            for error_str in errors:
                try:
                    error_info = eval(error_str)  # تحويل النص إلى قاموس
                    error_type = error_info.get("error_type", "Unknown")
                    error_types[error_type] = error_types.get(error_type, 0) + 1
                    
                    if len(recent_errors) < 5:
                        recent_errors.append(error_info)
                except:
                    continue
            
            return {
                "total_errors": len(errors),
                "error_types": error_types,
                "recent_errors": recent_errors,
                "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
            }
            
        except Exception as e:
            music_logger.error(f"فشل في الحصول على إحصائيات الأخطاء: {e}")
            return {}


# إنشاء معالج الأخطاء العام
error_handler = MusicErrorHandler()


async def safe_execute(func, *args, **kwargs):
    """تنفيذ دالة بشكل آمن مع معالجة الأخطاء"""
    try:
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    except Exception as e:
        await error_handler.log_error(e, {
            "function": func.__name__,
            "args": str(args),
            "kwargs": str(kwargs)
        })
        return None


def handle_music_errors(func):
    """ديكوريتر لمعالجة أخطاء الموسيقى"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # استخراج معلومات السياق
            context = {}
            if args and hasattr(args[0], '__class__'):
                context["class"] = args[0].__class__.__name__
            if len(args) > 1 and isinstance(args[1], Message):
                message = args[1]
                context.update({
                    "chat_id": message.chat.id,
                    "user_id": message.from_user.id if message.from_user else None,
                    "message_text": message.text[:100] if message.text else None
                })
            
            await error_handler.log_error(e, context)
            
            # إرسال رسالة خطأ للمستخدم
            if len(args) > 1 and isinstance(args[1], Message):
                try:
                    await args[1].reply_text(
                        f"❌ **حدث خطأ غير متوقع:**\n"
                        f"`{type(e).__name__}: {str(e)}`\n\n"
                        f"تم تسجيل الخطأ وسيتم إصلاحه قريباً."
                    )
                except:
                    pass
            
            return None
    return wrapper


async def log_music_action(action: str, chat_id: int, user_id: int, details: Dict[str, Any] = None):
    """تسجيل إجراء موسيقى"""
    await error_handler.log_action(action, chat_id, user_id, details)


async def get_music_logs(limit: int = 50) -> Dict[str, Any]:
    """الحصول على سجلات الموسيقى"""
    try:
        redis = await get_redis()
        
        # الحصول على الأخطاء
        errors = await redis.lrange("music_errors", 0, limit - 1)
        error_list = []
        for error_str in errors:
            try:
                error_list.append(eval(error_str))
            except:
                continue
        
        # الحصول على الإجراءات
        actions = await redis.lrange("music_actions", 0, limit - 1)
        action_list = []
        for action_str in actions:
            try:
                action_list.append(eval(action_str))
            except:
                continue
        
        # الحصول على الإحصائيات
        stats = await error_handler.get_error_stats()
        
        return {
            "errors": error_list,
            "actions": action_list,
            "stats": stats,
            "total_logs": len(error_list) + len(action_list)
        }
        
    except Exception as e:
        music_logger.error(f"فشل في الحصول على السجلات: {e}")
        return {}


async def clear_music_logs():
    """مسح سجلات الموسيقى"""
    try:
        redis = await get_redis()
        await redis.delete("music_errors")
        await redis.delete("music_actions")
        music_logger.info("تم مسح سجلات الموسيقى")
        return True
    except Exception as e:
        music_logger.error(f"فشل في مسح السجلات: {e}")
        return False


class MusicPerformanceMonitor:
    """مراقب أداء نظام الموسيقى"""
    
    def __init__(self):
        self.start_times = {}
        self.performance_data = []
    
    async def start_timing(self, operation: str, context: Dict[str, Any] = None):
        """بدء توقيت عملية"""
        self.start_times[operation] = {
            "start_time": datetime.now(),
            "context": context or {}
        }
    
    async def end_timing(self, operation: str):
        """إنهاء توقيت عملية"""
        if operation in self.start_times:
            start_data = self.start_times[operation]
            duration = (datetime.now() - start_data["start_time"]).total_seconds()
            
            performance_info = {
                "operation": operation,
                "duration": duration,
                "timestamp": datetime.now().isoformat(),
                "context": start_data["context"]
            }
            
            self.performance_data.append(performance_info)
            if len(self.performance_data) > 100:
                self.performance_data.pop(0)
            
            # تسجيل العمليات البطيئة
            if duration > 5.0:  # أكثر من 5 ثوان
                music_logger.warning(
                    f"عملية بطيئة: {operation} استغرقت {duration:.2f} ثانية"
                )
            
            del self.start_times[operation]
            return duration
        return None
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        if not self.performance_data:
            return {}
        
        durations = [data["duration"] for data in self.performance_data]
        operations = {}
        
        for data in self.performance_data:
            op = data["operation"]
            if op not in operations:
                operations[op] = []
            operations[op].append(data["duration"])
        
        return {
            "total_operations": len(self.performance_data),
            "average_duration": sum(durations) / len(durations),
            "max_duration": max(durations),
            "min_duration": min(durations),
            "operations_stats": {
                op: {
                    "count": len(times),
                    "avg_duration": sum(times) / len(times),
                    "max_duration": max(times)
                }
                for op, times in operations.items()
            }
        }


# إنشاء مراقب الأداء العام
performance_monitor = MusicPerformanceMonitor()


def monitor_performance(operation_name: str):
    """ديكوريتر لمراقبة أداء العمليات"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            await performance_monitor.start_timing(operation_name)
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                await performance_monitor.end_timing(operation_name)
        return wrapper
    return decorator
