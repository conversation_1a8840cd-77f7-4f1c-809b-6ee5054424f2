

بوت تليجرام للموسيقى مبني بلغة Python باستخدام مكتبات Pyrogram و PyroMod و Redis.



- تشغيل الموسيقى في المكالمات الصوتية
- دعم تشغيل الموسيقى من يوتيوب
- نظام رتب متكامل (مطور السورس، مطور، ادمن، VIP، عضو)
- إعدادات قابلة للتخصيص من داخل البوت
- تخزين البيانات باستخدام Redis
- واجهة عربية سهلة الاستخدام
- نظام إذاعات متكامل
- نظام تحكم في الحسابات المساعدة
- نظام سجل تشغيل
- نظام ترويج
- أوامر تسلية (صراحة، تويت، معلومة دينية)



- Python 3.8+
- Redis Server
- حساب تليجرام
- مفاتيح API من [my.telegram.org](https://my.telegram.org)



1. قم بنسخ المستودع:

```bash
git clone https://github.com/yourusername/music-bot.git
cd music-bot
```

2. قم بتثبيت المتطلبات:

```bash
pip install -r requirements.txt
```

3. قم بتعديل ملف `config.py` وإضافة المعلومات المطلوبة:

```python

API_ID = 12345  
API_HASH = "abcdef1234567890abcdef1234567890"  
BOT_TOKEN = "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ"  


REDIS_URI = "redis://localhost:6379/0"


OWNER_ID = 1234567890  
```

4. قم بتشغيل البوت:

```bash
python main.py
```




- `/start` - بدء البوت
- `/help` - عرض قائمة المساعدة
- `السورس` - عرض معلومات السورس
- `مطور السورس` - عرض معلومات المطور
- `صراحة` - لعبة صراحة
- `تويت` - لعبة تويت
- `معلومة دينية` - عرض معلومة دينية


- `/play` أو `تشغيل` - تشغيل أغنية من يوتيوب
- `/skip` أو `تخطي` - تخطي الأغنية الحالية
- `/pause` أو `ايقاف مؤقت` - إيقاف التشغيل مؤقتًا
- `/resume` أو `استئناف` - استئناف التشغيل
- `/stop` أو `ايقاف` - إيقاف التشغيل
- `/queue` أو `قائمة التشغيل` - عرض قائمة الانتظار


- `/settings` أو `اعدادات` - عرض إعدادات البوت
- `تعيين اسم البوت` - تغيير اسم البوت
- `تعيين اسم السورس` - تغيير اسم السورس
- `تعيين اسم المطور` - تغيير اسم المطور
- `تعيين قناة البوت` - تغيير قناة البوت
- `تعيين مجموعة البوت` - تغيير مجموعة البوت
- `الاشتراك الاجباري` - تفعيلتعطيل الاشتراك الإجباري
- `صلاحيات التشغيل` - تفعيلتعطيل صلاحيات التشغيل المتقدمة
- `التواصل` - تفعيلتعطيل التواصل


- `عدد المستخدمين` - عرض عدد المستخدمين
- `عدد المجموعات` - عرض عدد المجموعات
- `الاحصائيات` - عرض الإحصائيات العامة
- `قائمة المستخدمين` - عرض قائمة المستخدمين
- `قائمة المجموعات` - عرض قائمة المجموعات


- `اذاعة` - إرسال إذاعة عامة
- `اذاعة للمستخدمين` - إرسال إذاعة للمستخدمين
- `اذاعة للمجموعات` - إرسال إذاعة للمجموعات
- `اذاعة صوتية` - إرسال إذاعة صوتية


- `حظر` - حظر مستخدم
- `الغاء حظر` - إلغاء حظر مستخدم
- `حظر مجموعة` - حظر مجموعة
- `الغاء حظر مجموعة` - إلغاء حظر مجموعة
- `قائمة المحظورين` - عرض قائمة المستخدمين المحظورين
- `قائمة المجموعات المحظورة` - عرض قائمة المجموعات المحظورة


- `اضافة حساب مساعد` - إضافة حساب مساعد جديد
- `حذف حساب مساعد` - حذف حساب مساعد
- `الحسابات المساعدة` - عرض قائمة الحسابات المساعدة
- `دعوة المساعد` - دعوة الحساب المساعد إلى المجموعة
- `خروج المساعد` - خروج الحساب المساعد من المجموعات


- `تفعيل سجل التشغيل` - تفعيل سجل التشغيل
- `تعطيل سجل التشغيل` - تعطيل سجل التشغيل
- `تعيين قناة السجل` - تعيين قناة سجل التشغيل


- `ترويج عام` - تعيين ترويج عام
- `ترويج لأذان` - تعيين ترويج للأذان
- `ترويج لحماية` - تعيين ترويج للحماية
- `ترويج لتحديث` - تعيين ترويج للتحديث
- `الغاء الترويج` - إلغاء الترويج



```
music-bot/
├── config.py             
├── main.py               
├── requirements.txt      

├── database/             
│   ├── redis_db.py       
│   ├── users_db.py       
│   ├── groups_db.py      
│   └── settings_db.py    
├── helpers/              
│   ├── decorators.py     
│   ├── keyboards.py      
│   └── filters.py        
├── plugins/              
│   ├── admin/            
│   ├── dev/              
│   ├── user/             
│   └── music/            
└── utils/                
    ├── extraction.py     
    ├── thumbnails.py     
    └── logger.py         
```



المساهمات مرحب بها! يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمستودع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب سحب



هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.



إذا كان لديك أي أسئلة أو اقتراحات، يرجى التواصل معي عبر:

- تليجرام: [@yourusername](https://t.me/yourusername)
- البريد الإلكتروني: <EMAIL>
