from pyrogram import Client, filters
from pyrogram.types import Message, ReplyKeyboardMarkup, KeyboardButton
from helpers.decorators import dev_only
from helpers.filters import dev
from helpers.command_handler import command_handler
from database.settings_db import (
    get_bot_status_settings,
    set_bot_status_reply_with_photo,
    set_bot_status_custom_message
)


@Client.on_message(command_handler(["اعدادات البوت", "bot settings"]) & filters.private & dev)
@dev_only
async def bot_status_settings_menu(client: Client, message: Message):
    """قائمة إعدادات أمر البوت"""
    settings = await get_bot_status_settings()
    
    # إنشاء الكيبورد
    keyboard = ReplyKeyboardMarkup([
        [KeyboardButton("🖼️ تفعيل الرد بالصورة"), KeyboardButton("📝 تعطيل الرد بالصورة")],
        [KeyboardButton("✏️ تعيين رسالة مخصصة"), KeyboardButton("🗑️ حذف الرسالة المخصصة")],
        [KeyboardButton("👀 عرض الإعدادات الحالية")],
        [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
    ], resize_keyboard=True)
    
    await message.reply_text(
        "⚙️ **إعدادات أمر البوت**\n\n"
        "يمكنك تخصيص كيفية رد البوت على أمر 'بوت':\n\n"
        "🖼️ **الرد بالصورة:** عرض صورة البوت مع الرسالة\n"
        "📝 **الرسالة المخصصة:** تعيين رسالة مخصصة بدلاً من الافتراضية\n\n"
        "اختر الإعداد الذي تريد تغييره:",
        reply_markup=keyboard
    )


@Client.on_message(filters.text & filters.regex("^🖼️ تفعيل الرد بالصورة$") & filters.private & dev)
@dev_only
async def enable_photo_reply(client: Client, message: Message):
    """تفعيل الرد بصورة البوت"""
    await set_bot_status_reply_with_photo(True)
    await message.reply_text(
        "✅ **تم تفعيل الرد بصورة البوت بنجاح.**\n\n"
        "الآن سيرد البوت على أمر 'بوت' بصورة البوت مع الرسالة."
    )


@Client.on_message(filters.text & filters.regex("^📝 تعطيل الرد بالصورة$") & filters.private & dev)
@dev_only
async def disable_photo_reply(client: Client, message: Message):
    """تعطيل الرد بصورة البوت"""
    await set_bot_status_reply_with_photo(False)
    await message.reply_text(
        "✅ **تم تعطيل الرد بصورة البوت بنجاح.**\n\n"
        "الآن سيرد البوت على أمر 'بوت' برسالة نصية فقط."
    )


@Client.on_message(filters.text & filters.regex("^✏️ تعيين رسالة مخصصة$") & filters.private & dev)
@dev_only
async def set_custom_message_prompt(client: Client, message: Message):
    """طلب تعيين رسالة مخصصة"""
    await message.reply_text(
        "✏️ **تعيين رسالة مخصصة لأمر البوت**\n\n"
        "أرسل الرسالة التي تريد أن يرد بها البوت على أمر 'بوت'.\n\n"
        "**ملاحظات:**\n"
        "• يمكن استخدام النصوص والرموز التعبيرية\n"
        "• يمكن استخدام تنسيق Markdown\n"
        "• أرسل 'إلغاء' للإلغاء\n\n"
        "**مثال:**\n"
        "`🤖 مرحباً! أنا بوت الموسيقى وأعمل بشكل ممتاز! 🎵`"
    )
    
    # تعيين حالة انتظار الرسالة المخصصة
    client.waiting_for_custom_message = message.from_user.id


@Client.on_message(filters.text & filters.regex("^👀 عرض الإعدادات الحالية$") & filters.private & dev)
@dev_only
async def show_current_settings(client: Client, message: Message):
    """عرض الإعدادات الحالية"""
    settings = await get_bot_status_settings()

    photo_status = "🖼️ مفعل" if settings.get("reply_with_photo", True) else "📝 معطل"
    custom_message = settings.get("custom_message")

    settings_text = f"""
👀 **الإعدادات الحالية لأمر البوت:**

🖼️ **الرد بالصورة:** {photo_status}

📝 **الرسالة المخصصة:**
"""

    if custom_message:
        settings_text += f"✅ **مفعلة**\n\n**الرسالة:**\n{custom_message}"
    else:
        settings_text += "❌ **غير مفعلة** (يستخدم الرسالة الافتراضية)"

    await message.reply_text(settings_text)


@Client.on_message(filters.text & filters.regex("^🔙 العودة للقائمة الرئيسية$") & filters.private & dev)
@dev_only
async def back_to_main_menu(client: Client, message: Message):
    """العودة للقائمة الرئيسية"""
    from plugins.dev.assistant_keyboard_interface import assistant_management_start
    await assistant_management_start(client, message)


@Client.on_message(filters.text & filters.private & dev, group=99)
@dev_only
async def handle_custom_message_input(client: Client, message: Message):
    """معالجة إدخال الرسالة المخصصة"""
    # التحقق من وجود حالة انتظار
    if not hasattr(client, 'waiting_for_custom_message'):
        return

    if client.waiting_for_custom_message != message.from_user.id:
        return

    # إزالة حالة الانتظار
    delattr(client, 'waiting_for_custom_message')

    # التحقق من الإلغاء
    if message.text.lower() in ['إلغاء', 'cancel']:
        await message.reply_text("❌ **تم إلغاء تعيين الرسالة المخصصة.**")
        return

    # حفظ الرسالة المخصصة
    custom_message = message.text
    await set_bot_status_custom_message(custom_message)

    await message.reply_text(
        "✅ **تم تعيين الرسالة المخصصة بنجاح.**\n\n"
        f"**الرسالة الجديدة:**\n{custom_message}\n\n"
        "الآن سيرد البوت بهذه الرسالة على أمر 'بوت'."
    )


@Client.on_message(filters.text & filters.regex("^🗑️ حذف الرسالة المخصصة$") & filters.private & dev)
@dev_only
async def delete_custom_message(client: Client, message: Message):
    """حذف الرسالة المخصصة"""
    await set_bot_status_custom_message(None)
    await message.reply_text(
        "✅ **تم حذف الرسالة المخصصة بنجاح.**\n\n"
        "الآن سيرد البوت بالرسالة الافتراضية على أمر 'بوت'."
    )
